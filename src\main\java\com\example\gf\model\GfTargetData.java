package com.example.gf.model;

import java.sql.Timestamp;

/**
 * 目标表数据模型
 */
public class GfTargetData {
    
    private Integer pointId;
    private Timestamp dataTime;
    private String dataType;
    private String qn;
    private String st;
    private String mncode;
    private String flag;
    private String cpExt;
    private String geneJson;
    private String mainExt;
    private String autoAudit;
    private String extProperty;
    private String pointType;
    private Timestamp writeTime;
    private String source;
    private String operationCustomerId;
    private String geneJsonStd;
    private String waterGeneLabel;
    private String waterLabel;
    private String commRule;
    private String labelJson;
    private String geneTransJson;
    
    // 默认构造函数
    public GfTargetData() {}
    
    // Getters and Setters
    public Integer getPointId() {
        return pointId;
    }
    
    public void setPointId(Integer pointId) {
        this.pointId = pointId;
    }
    
    public Timestamp getDataTime() {
        return dataTime;
    }
    
    public void setDataTime(Timestamp dataTime) {
        this.dataTime = dataTime;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public String getQn() {
        return qn;
    }
    
    public void setQn(String qn) {
        this.qn = qn;
    }
    
    public String getSt() {
        return st;
    }
    
    public void setSt(String st) {
        this.st = st;
    }
    
    public String getMncode() {
        return mncode;
    }
    
    public void setMncode(String mncode) {
        this.mncode = mncode;
    }
    
    public String getFlag() {
        return flag;
    }
    
    public void setFlag(String flag) {
        this.flag = flag;
    }
    
    public String getCpExt() {
        return cpExt;
    }
    
    public void setCpExt(String cpExt) {
        this.cpExt = cpExt;
    }
    
    public String getGeneJson() {
        return geneJson;
    }
    
    public void setGeneJson(String geneJson) {
        this.geneJson = geneJson;
    }
    
    public String getMainExt() {
        return mainExt;
    }
    
    public void setMainExt(String mainExt) {
        this.mainExt = mainExt;
    }
    
    public String getAutoAudit() {
        return autoAudit;
    }
    
    public void setAutoAudit(String autoAudit) {
        this.autoAudit = autoAudit;
    }
    
    public String getExtProperty() {
        return extProperty;
    }
    
    public void setExtProperty(String extProperty) {
        this.extProperty = extProperty;
    }
    
    public String getPointType() {
        return pointType;
    }
    
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    
    public Timestamp getWriteTime() {
        return writeTime;
    }
    
    public void setWriteTime(Timestamp writeTime) {
        this.writeTime = writeTime;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getOperationCustomerId() {
        return operationCustomerId;
    }
    
    public void setOperationCustomerId(String operationCustomerId) {
        this.operationCustomerId = operationCustomerId;
    }
    
    public String getGeneJsonStd() {
        return geneJsonStd;
    }
    
    public void setGeneJsonStd(String geneJsonStd) {
        this.geneJsonStd = geneJsonStd;
    }
    
    public String getWaterGeneLabel() {
        return waterGeneLabel;
    }
    
    public void setWaterGeneLabel(String waterGeneLabel) {
        this.waterGeneLabel = waterGeneLabel;
    }
    
    public String getWaterLabel() {
        return waterLabel;
    }
    
    public void setWaterLabel(String waterLabel) {
        this.waterLabel = waterLabel;
    }
    
    public String getCommRule() {
        return commRule;
    }
    
    public void setCommRule(String commRule) {
        this.commRule = commRule;
    }
    
    public String getLabelJson() {
        return labelJson;
    }
    
    public void setLabelJson(String labelJson) {
        this.labelJson = labelJson;
    }
    
    public String getGeneTransJson() {
        return geneTransJson;
    }
    
    public void setGeneTransJson(String geneTransJson) {
        this.geneTransJson = geneTransJson;
    }
    
    @Override
    public String toString() {
        return "GfTargetData{" +
                "pointId=" + pointId +
                ", dataTime=" + dataTime +
                ", dataType='" + dataType + '\'' +
                ", mncode='" + mncode + '\'' +
                ", pointType='" + pointType + '\'' +
                ", operationCustomerId='" + operationCustomerId + '\'' +
                '}';
    }
}
