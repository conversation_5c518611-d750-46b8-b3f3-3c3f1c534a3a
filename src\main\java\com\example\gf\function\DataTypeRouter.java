package com.example.gf.function;

import com.example.gf.model.GfTargetData;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据类型路由器
 * 根据data_type将数据分流到不同的输出流
 */
public class DataTypeRouter extends ProcessFunction<GfTargetData, GfTargetData> {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataTypeRouter.class);
    
    // 定义侧输出流标签
    public static final OutputTag<GfTargetData> MINUTE_DATA_TAG = 
            new OutputTag<GfTargetData>("minute-data") {};
    
    public static final OutputTag<GfTargetData> HOUR_DATA_TAG = 
            new OutputTag<GfTargetData>("hour-data") {};
    
    public static final OutputTag<GfTargetData> DAY_DATA_TAG = 
            new OutputTag<GfTargetData>("day-data") {};
    
    @Override
    public void processElement(GfTargetData value, Context ctx, Collector<GfTargetData> out) throws Exception {
        String dataType = value.getDataType();
        
        if (dataType == null) {
            LOG.warn("Data type is null, outputting to main stream for error handling: {}", value);
            out.collect(value);
            return;
        }
        
        switch (dataType) {
            case "2051": // 分钟数据
                ctx.output(MINUTE_DATA_TAG, value);
                LOG.debug("Routed minute data: pointId={}, dataTime={}", 
                        value.getPointId(), value.getDataTime());
                break;
                
            case "2061": // 小时数据
                ctx.output(HOUR_DATA_TAG, value);
                LOG.debug("Routed hour data: pointId={}, dataTime={}", 
                        value.getPointId(), value.getDataTime());
                break;
                
            case "2031": // 日数据
                ctx.output(DAY_DATA_TAG, value);
                LOG.debug("Routed day data: pointId={}, dataTime={}", 
                        value.getPointId(), value.getDataTime());
                break;
                
            default:
                LOG.warn("Unknown data type: {}, outputting to main stream for error handling: {}", 
                        dataType, value);
                out.collect(value);
        }
    }
}
