package com.example.gf.model;

import java.sql.Timestamp;

/**
 * 站点信息模型
 */
public class PointInfo {
    
    private Integer pointId;
    private String mnCode;
    private String pointType;
    private String operationCustomerId;
    private String companyId;
    private String dataPwd;
    private String customerId;
    private String dataFlag;
    private String waterSamplingType;
    private String geneCodeMapping;
    private Timestamp writeTime;
    
    // 默认构造函数
    public PointInfo() {}
    
    // 全参构造函数
    public PointInfo(Integer pointId, String mnCode, String pointType, 
                     String operationCustomerId, String companyId, String dataPwd,
                     String customerId, String dataFlag, String waterSamplingType,
                     String geneCodeMapping, Timestamp writeTime) {
        this.pointId = pointId;
        this.mnCode = mnCode;
        this.pointType = pointType;
        this.operationCustomerId = operationCustomerId;
        this.companyId = companyId;
        this.dataPwd = dataPwd;
        this.customerId = customerId;
        this.dataFlag = dataFlag;
        this.waterSamplingType = waterSamplingType;
        this.geneCodeMapping = geneCodeMapping;
        this.writeTime = writeTime;
    }
    
    // Getters and Setters
    public Integer getPointId() {
        return pointId;
    }
    
    public void setPointId(Integer pointId) {
        this.pointId = pointId;
    }
    
    public String getMnCode() {
        return mnCode;
    }
    
    public void setMnCode(String mnCode) {
        this.mnCode = mnCode;
    }
    
    public String getPointType() {
        return pointType;
    }
    
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    
    public String getOperationCustomerId() {
        return operationCustomerId;
    }
    
    public void setOperationCustomerId(String operationCustomerId) {
        this.operationCustomerId = operationCustomerId;
    }
    
    public String getCompanyId() {
        return companyId;
    }
    
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
    
    public String getDataPwd() {
        return dataPwd;
    }
    
    public void setDataPwd(String dataPwd) {
        this.dataPwd = dataPwd;
    }
    
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    
    public String getDataFlag() {
        return dataFlag;
    }
    
    public void setDataFlag(String dataFlag) {
        this.dataFlag = dataFlag;
    }
    
    public String getWaterSamplingType() {
        return waterSamplingType;
    }
    
    public void setWaterSamplingType(String waterSamplingType) {
        this.waterSamplingType = waterSamplingType;
    }
    
    public String getGeneCodeMapping() {
        return geneCodeMapping;
    }
    
    public void setGeneCodeMapping(String geneCodeMapping) {
        this.geneCodeMapping = geneCodeMapping;
    }
    
    public Timestamp getWriteTime() {
        return writeTime;
    }
    
    public void setWriteTime(Timestamp writeTime) {
        this.writeTime = writeTime;
    }
    
    @Override
    public String toString() {
        return "PointInfo{" +
                "pointId=" + pointId +
                ", mnCode='" + mnCode + '\'' +
                ", pointType='" + pointType + '\'' +
                ", operationCustomerId='" + operationCustomerId + '\'' +
                ", geneCodeMapping='" + geneCodeMapping + '\'' +
                '}';
    }
}
