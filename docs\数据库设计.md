# 数据库设计文档

## 1. 数据库架构概述

### 1.1 整体架构
- **数据库**: PostgreSQL 12+
- **Schema**: env
- **分区策略**: 复合分区表（LIST + RANGE）
- **索引策略**: 主键 + 时间索引 + 业务索引

### 1.2 表结构设计原则
- 使用复合分区提高查询性能
- JSONB字段存储动态数据
- 主键保证数据唯一性
- 索引优化常用查询路径

## 2. 源表设计

### 2.1 站点信息表 (env.env_points_flink)

```sql
-- 站点信息基础表
CREATE TABLE env.env_points_flink (
    point_id INTEGER NOT NULL,                    -- 站点ID
    mn_code VARCHAR(50) NOT NULL,                 -- 设备MN码
    point_type VARCHAR(10) NOT NULL,              -- 站点类型
    operation_customer_id VARCHAR(32),            -- 运维企业ID
    company_id VARCHAR(32),                       -- 企业ID
    data_pwd VARCHAR(20),                         -- 数据密码
    customer_id VARCHAR(32),                      -- 客户ID
    data_flag VARCHAR(50),                        -- 数据标识
    water_sampling_type VARCHAR(10),              -- 水质采样类型
    gene_code_mapping TEXT,                       -- 因子编码映射JSON
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 写入时间
    
    CONSTRAINT env_points_flink_pkey PRIMARY KEY (point_id),
    CONSTRAINT env_points_flink_mn_unique UNIQUE (mn_code, data_pwd)
);

-- 创建索引
CREATE INDEX idx_env_points_flink_mn_pwd ON env.env_points_flink (mn_code, data_pwd);
CREATE INDEX idx_env_points_flink_point_type ON env.env_points_flink (point_type);
CREATE INDEX idx_env_points_flink_operation_customer ON env.env_points_flink (operation_customer_id);

-- 表注释
COMMENT ON TABLE env.env_points_flink IS '站点信息基础表，用于Flink作业查询';
COMMENT ON COLUMN env.env_points_flink.gene_code_mapping IS '因子编码映射，JSON格式：{"011": "NC68", "065": "NC92"}';
```

## 3. 目标表设计

### 3.1 父表结构模板

```sql
-- 国发数据父表模板 (以小时数据为例)
CREATE TABLE env.env_2061_gf (
    point_id INTEGER NOT NULL,                    -- 站点ID
    data_time TIMESTAMP NOT NULL,                 -- 数据时间
    data_type VARCHAR(10) NOT NULL,               -- 数据类型 (cn字段)
    qn VARCHAR(30),                               -- 请求编码 (id字段)
    st VARCHAR(10),                               -- 系统编码
    mncode VARCHAR(50) NOT NULL,                  -- 设备MN码 (mn字段)
    flag VARCHAR(10),                             -- 拆分包及应答标志
    cp_ext JSONB,                                 -- 指令参数CP中的非因子监测数据
    gene_json JSONB,                              -- 因子数据JSON (factors字段)
    main_ext JSONB,                               -- 数据段中非固定字段数据
    auto_audit JSONB,                             -- 自动审核质控数据
    ext_property JSONB,                           -- 报文附加属性
    point_type VARCHAR(10) NOT NULL,              -- 站点类型
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 报文写入时间
    source VARCHAR(30) DEFAULT 'nt',              -- 来源标识
    operation_customer_id VARCHAR(32),            -- 运维企业ID
    gene_json_std JSONB,                          -- 浓度和排放量标准化数据
    water_gene_label JSONB,                       -- 水质因子标签
    water_label JSONB,                            -- 水质报文标签
    comm_rule JSONB,                              -- 通用异常标签
    label_json JSONB,                             -- 标签JSON
    gene_trans_json JSONB DEFAULT '{}'::jsonb,    -- 因子编码转换映射
    
    CONSTRAINT env_2061_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2061_gf IS '小时报文正式表（国发）';
COMMENT ON COLUMN env.env_2061_gf.gene_trans_json IS '因子编码转换映射，格式：{"NC68": "011", "NC92": "065"}';
```

### 3.2 分区表设计

#### 3.2.1 一级分区表 (按point_type分区)

```sql
-- 小时数据 point_type=31 分区
CREATE TABLE env.env_2061_gf_31 PARTITION OF env.env_2061_gf 
FOR VALUES IN ('31')
PARTITION BY RANGE (data_time);

COMMENT ON TABLE env.env_2061_gf_31 IS '小时数据正式表（国发）- 站点类型31';

-- 小时数据 point_type=32 分区
CREATE TABLE env.env_2061_gf_32 PARTITION OF env.env_2061_gf 
FOR VALUES IN ('32')
PARTITION BY RANGE (data_time);

COMMENT ON TABLE env.env_2061_gf_32 IS '小时数据正式表（国发）- 站点类型32';
```

#### 3.2.2 二级分区表 (按时间分区)

```sql
-- 小时数据按年分区示例
CREATE TABLE env.env_2061_gf_31_2025 PARTITION OF env.env_2061_gf_31 (
    CONSTRAINT env_2061_gf_31_2025_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2026-01-01 00:00:00');

-- 创建时间索引
CREATE INDEX idx_env_2061_gf_31_2025_dt ON env.env_2061_gf_31_2025 USING btree (data_time);
CREATE INDEX idx_env_2061_gf_31_2025_point_id ON env.env_2061_gf_31_2025 USING btree (point_id);
CREATE INDEX idx_env_2061_gf_31_2025_mncode ON env.env_2061_gf_31_2025 USING btree (mncode);

COMMENT ON TABLE env.env_2061_gf_31_2025 IS '小时数据正式表（国发）- 站点类型31 - 2025年';
```

### 3.3 完整表结构定义

#### 3.3.1 分钟数据表 (env_2051_gf)

```sql
-- 分钟数据父表
CREATE TABLE env.env_2051_gf (
    point_id INTEGER NOT NULL,
    data_time TIMESTAMP NOT NULL,
    data_type VARCHAR(10) NOT NULL,
    qn VARCHAR(30),
    st VARCHAR(10),
    mncode VARCHAR(50) NOT NULL,
    flag VARCHAR(10),
    cp_ext JSONB,
    gene_json JSONB,
    main_ext JSONB,
    auto_audit JSONB,
    ext_property JSONB,
    point_type VARCHAR(10) NOT NULL,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(30) DEFAULT 'nt',
    operation_customer_id VARCHAR(32),
    gene_json_std JSONB,
    water_gene_label JSONB,
    water_label JSONB,
    comm_rule JSONB,
    label_json JSONB,
    gene_trans_json JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT env_2051_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2051_gf IS '分钟报文正式表（国发）';

-- 分钟数据按月分区示例
CREATE TABLE env.env_2051_gf_31 PARTITION OF env.env_2051_gf 
FOR VALUES IN ('31')
PARTITION BY RANGE (data_time);

CREATE TABLE env.env_2051_gf_31_2025_01 PARTITION OF env.env_2051_gf_31 (
    CONSTRAINT env_2051_gf_31_2025_01_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-02-01 00:00:00');
```

#### 3.3.2 日数据表 (env_2031_gf)

```sql
-- 日数据父表 (不做时间分区)
CREATE TABLE env.env_2031_gf (
    point_id INTEGER NOT NULL,
    data_time TIMESTAMP NOT NULL,
    data_type VARCHAR(10) NOT NULL,
    qn VARCHAR(30),
    st VARCHAR(10),
    mncode VARCHAR(50) NOT NULL,
    flag VARCHAR(10),
    cp_ext JSONB,
    gene_json JSONB,
    main_ext JSONB,
    auto_audit JSONB,
    ext_property JSONB,
    point_type VARCHAR(10) NOT NULL,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(30) DEFAULT 'nt',
    operation_customer_id VARCHAR(32),
    gene_json_std JSONB,
    water_gene_label JSONB,
    water_label JSONB,
    comm_rule JSONB,
    label_json JSONB,
    gene_trans_json JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT env_2031_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2031_gf IS '日报文正式表（国发）';

-- 日数据只做point_type分区
CREATE TABLE env.env_2031_gf_31 PARTITION OF env.env_2031_gf 
FOR VALUES IN ('31');

CREATE INDEX idx_env_2031_gf_31_dt ON env.env_2031_gf_31 USING btree (data_time);
CREATE INDEX idx_env_2031_gf_31_point_id ON env.env_2031_gf_31 USING btree (point_id);
```

## 4. 分区管理策略

### 4.1 分区创建规则

| 数据类型 | 表名 | 一级分区 | 二级分区 | 分区间隔 |
|---------|------|----------|----------|----------|
| 2051 | env_2051_gf | point_type | data_time | 按月 |
| 2061 | env_2061_gf | point_type | data_time | 按年 |
| 2031 | env_2031_gf | point_type | 无 | 无 |

### 4.2 分区命名规范

```sql
-- 命名模板
-- 一级分区: env_{data_type}_gf_{point_type}
-- 二级分区: env_{data_type}_gf_{point_type}_{time_suffix}

-- 示例:
-- env_2051_gf_31_2025_01  (分钟数据, point_type=31, 2025年1月)
-- env_2061_gf_31_2025     (小时数据, point_type=31, 2025年)
-- env_2031_gf_31          (日数据, point_type=31, 无时间分区)
```

### 4.3 分区维护SQL

```sql
-- 检查分区是否存在
SELECT schemaname, tablename 
FROM pg_tables 
WHERE schemaname = 'env' 
  AND tablename LIKE 'env_2061_gf_%';

-- 查看分区信息
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'env' 
  AND tablename LIKE 'env_%_gf_%'
ORDER BY tablename;

-- 删除旧分区 (示例: 删除6个月前的分钟数据分区)
DROP TABLE IF EXISTS env.env_2051_gf_31_2024_07;
```

## 5. 索引设计

### 5.1 主键索引
- 所有表都有复合主键：(point_id, data_time, operation_customer_id, data_type, source)
- 保证数据唯一性，支持幂等写入

### 5.2 业务索引

```sql
-- 时间查询索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_dt 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} (data_time);

-- 站点查询索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_point_id 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} (point_id);

-- MN码查询索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_mncode 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} (mncode);

-- 运维企业查询索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_operation_customer 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} (operation_customer_id);
```

### 5.3 JSONB字段索引

```sql
-- 因子数据GIN索引 (支持JSON查询)
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_gene_json 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} USING gin (gene_json);

-- 因子转换映射GIN索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_{time_suffix}_gene_trans 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} USING gin (gene_trans_json);
```

## 6. 数据类型说明

### 6.1 基础字段类型

| 字段名 | 数据类型 | 长度 | 说明 |
|--------|----------|------|------|
| point_id | INTEGER | 4字节 | 站点ID |
| data_time | TIMESTAMP | 8字节 | 数据时间 |
| data_type | VARCHAR(10) | 变长 | 数据类型(2051/2061/2031) |
| mncode | VARCHAR(50) | 变长 | 设备MN码 |
| point_type | VARCHAR(10) | 变长 | 站点类型 |

### 6.2 JSONB字段结构

#### gene_json (因子数据)
```json
{
  "011": {
    "factor_code": "011",
    "rtd_value": "",
    "avg_value": "97.38",
    "min_value": "",
    "max_value": "",
    "cou_value": ""
  },
  "101": {
    "factor_code": "101", 
    "avg_value": "198.53"
  }
}
```

#### main_ext (扩展数据)
```json
{
  "pwd": "654321",
  "outlet_code": "",
  "workcordSc": "",
  "zd_workcordSc": "",
  "log_time": ""
}
```

#### gene_trans_json (因子映射)
```json
{
  "NC68": "011",
  "NC92": "065", 
  "NC97": "101",
  "NC137": "B01"
}
```

## 7. 性能优化

### 7.1 分区裁剪优化
- 查询时指定 point_type 和 data_time 范围
- 利用分区裁剪减少扫描数据量

### 7.2 JSONB查询优化
```sql
-- 使用GIN索引查询JSONB
SELECT * FROM env.env_2061_gf_31_2025 
WHERE gene_json ? '011'  -- 检查是否包含因子011
  AND gene_json->'011'->>'avg_value' IS NOT NULL;

-- 使用JSONB路径查询
SELECT * FROM env.env_2061_gf_31_2025 
WHERE gene_trans_json @> '{"NC68": "011"}';
```

### 7.3 批量操作优化
```sql
-- 使用ON CONFLICT进行幂等写入
INSERT INTO env.env_2061_gf_31_2025 (...) 
VALUES (...) 
ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source) 
DO UPDATE SET 
  gene_json = EXCLUDED.gene_json,
  write_time = EXCLUDED.write_time;
```

## 8. 维护管理

### 8.1 分区清理策略
- 分钟数据：保留6个月
- 小时数据：保留2年  
- 日数据：保留5年

### 8.2 统计信息更新
```sql
-- 定期更新表统计信息
ANALYZE env.env_2061_gf_31_2025;

-- 重建索引 (必要时)
REINDEX INDEX idx_env_2061_gf_31_2025_dt;
```

### 8.3 监控查询
```sql
-- 查看分区大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_stat_get_tuples_inserted(c.oid) as inserts,
    pg_stat_get_tuples_updated(c.oid) as updates
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
WHERE schemaname = 'env' 
  AND tablename LIKE 'env_%_gf_%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

这份数据库设计文档详细描述了PostgreSQL分区表的设计方案，为数据存储和查询优化提供了完整的指导。
