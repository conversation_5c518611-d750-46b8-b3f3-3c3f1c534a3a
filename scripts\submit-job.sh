#!/bin/bash

# 国发数据处理作业提交脚本

# 设置环境变量
export FLINK_HOME=${FLINK_HOME:-/opt/flink}
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-8-openjdk}

# 作业配置
JOB_JAR="target/gf-data-processor-1.0.0.jar"
JOB_CLASS="com.example.gf.GfDataProcessorJob"
CONFIG_FILE="src/main/resources/application.properties"

# 检查Flink环境
check_flink_environment() {
    echo "Checking Flink environment..."
    
    if [ ! -d "$FLINK_HOME" ]; then
        echo "Error: FLINK_HOME directory not found: $FLINK_HOME"
        exit 1
    fi
    
    if [ ! -f "$FLINK_HOME/bin/flink" ]; then
        echo "Error: Flink binary not found: $FLINK_HOME/bin/flink"
        exit 1
    fi
    
    # 检查Flink集群状态
    if ! curl -f http://localhost:8081 > /dev/null 2>&1; then
        echo "Error: Flink cluster is not running. Please start Flink cluster first."
        echo "You can start it with: $FLINK_HOME/bin/start-cluster.sh"
        exit 1
    fi
    
    echo "✓ Flink environment is ready"
}

# 检查JAR文件
check_jar_file() {
    echo "Checking JAR file..."
    
    if [ ! -f "$JOB_JAR" ]; then
        echo "Error: JAR file not found: $JOB_JAR"
        echo "Please build the project first with: mvn clean package"
        exit 1
    fi
    
    echo "✓ JAR file found: $JOB_JAR"
}

# 检查配置文件
check_config_file() {
    echo "Checking configuration file..."
    
    if [ ! -f "$CONFIG_FILE" ]; then
        echo "Error: Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    echo "✓ Configuration file found: $CONFIG_FILE"
}

# 读取配置参数
read_config() {
    echo "Reading configuration..."
    
    # 从配置文件读取并行度配置
    DEFAULT_PARALLELISM=$(grep "parallelism.default" $CONFIG_FILE | cut -d'=' -f2 | tr -d ' ')
    SOURCE_PARALLELISM=$(grep "parallelism.source" $CONFIG_FILE | cut -d'=' -f2 | tr -d ' ')
    PROCESS_PARALLELISM=$(grep "parallelism.process" $CONFIG_FILE | cut -d'=' -f2 | tr -d ' ')
    SINK_PARALLELISM=$(grep "parallelism.sink" $CONFIG_FILE | cut -d'=' -f2 | tr -d ' ')
    
    # 设置默认值
    DEFAULT_PARALLELISM=${DEFAULT_PARALLELISM:-2}
    SOURCE_PARALLELISM=${SOURCE_PARALLELISM:-1}
    PROCESS_PARALLELISM=${PROCESS_PARALLELISM:-2}
    SINK_PARALLELISM=${SINK_PARALLELISM:-1}
    
    echo "Configuration loaded:"
    echo "  Default Parallelism: $DEFAULT_PARALLELISM"
    echo "  Source Parallelism: $SOURCE_PARALLELISM"
    echo "  Process Parallelism: $PROCESS_PARALLELISM"
    echo "  Sink Parallelism: $SINK_PARALLELISM"
}

# 提交作业
submit_job() {
    echo "Submitting Flink job..."
    
    # 构建提交命令
    SUBMIT_CMD="$FLINK_HOME/bin/flink run \
        --class $JOB_CLASS \
        --parallelism $DEFAULT_PARALLELISM \
        --detached \
        $JOB_JAR"
    
    echo "Executing command: $SUBMIT_CMD"
    
    # 执行提交
    $SUBMIT_CMD
    
    if [ $? -eq 0 ]; then
        echo "✓ Job submitted successfully"
        
        # 等待一下让作业启动
        sleep 5
        
        # 检查作业状态
        check_job_status
    else
        echo "✗ Failed to submit job"
        exit 1
    fi
}

# 检查作业状态
check_job_status() {
    echo "Checking job status..."
    
    # 获取作业列表
    JOBS_RESPONSE=$(curl -s http://localhost:8081/jobs)
    
    if [ $? -eq 0 ]; then
        # 检查是否有运行中的作业
        RUNNING_JOBS=$(echo $JOBS_RESPONSE | grep -o '"state":"RUNNING"' | wc -l)
        
        if [ $RUNNING_JOBS -gt 0 ]; then
            echo "✓ Job is running successfully"
            echo "You can monitor the job at: http://localhost:8081"
        else
            echo "⚠ Job may not be running. Please check Flink Web UI: http://localhost:8081"
        fi
    else
        echo "⚠ Unable to check job status. Please check Flink Web UI: http://localhost:8081"
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -c, --config   Specify configuration file (default: $CONFIG_FILE)"
    echo "  -j, --jar      Specify JAR file (default: $JOB_JAR)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Submit job with default settings"
    echo "  $0 -c custom.properties               # Submit job with custom config"
    echo "  $0 -j custom-job.jar                  # Submit job with custom JAR"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -j|--jar)
            JOB_JAR="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    echo "=== GF Data Processor Job Submission ==="
    echo "Starting job submission process..."
    echo ""
    
    check_flink_environment
    check_jar_file
    check_config_file
    read_config
    
    echo ""
    echo "All checks passed. Submitting job..."
    echo ""
    
    submit_job
    
    echo ""
    echo "=== Job Submission Complete ==="
}

# 执行主函数
main
