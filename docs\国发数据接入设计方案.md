# 国发数据接入设计方案

## 1. 项目概述

### 1.1 项目背景
使用 Flink 从 Kafka 读取 topic=t212_gf（国发数据），经过数据处理和转换后，写入到 PostgreSQL 表 env_{data_type}_gf。

### 1.2 技术栈
- **Flink**: 1.17.2
- **Kafka**: 2.x
- **PostgreSQL**: 12
- **Java**: 8+

### 1.3 核心挑战
- factors 字段中的因子编码是动态的，不固定
- PostgreSQL 分区表需要自动创建
- 根据 data_type 路由到不同的目标表
- 实时数据处理和容错机制

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    A[Kafka Topic: t212_gf] --> B[Flink Streaming Job]
    C[PostgreSQL: env.env_points_flink] --> B
    B --> D1[PostgreSQL: env.env_2051_gf]
    B --> D2[PostgreSQL: env.env_2061_gf] 
    B --> D3[PostgreSQL: env.env_2031_gf]
    
    subgraph "Flink Processing Pipeline"
        B1[Kafka Source] --> B2[JSON解析]
        B3[PostgreSQL Lookup] --> B4[数据关联]
        B2 --> B4
        B4 --> B5[字段映射转换]
        B5 --> B6[数据类型路由]
        B6 --> B7[分区检查]
        B7 --> B8[多表写入]
    end
    
    B --> B1
    B8 --> D1
    B8 --> D2
    B8 --> D3
```

### 2.2 数据流处理架构

#### 2.2.1 数据源配置
- **Kafka Source**: 消费 t212_gf topic，JSON 格式数据
- **PostgreSQL Lookup**: 查询站点基础信息表 env.env_points_flink
- **处理模式**: 实时流处理，exactly-once 语义

#### 2.2.2 核心处理组件
1. **JSON 解析器**: 处理动态 factors 字段
2. **数据关联器**: 基于 mn 和 pwd 关联站点信息
3. **字段映射器**: 构建目标表字段
4. **路由分发器**: 根据 data_type 分流到不同表
5. **分区管理器**: 自动创建和管理分区表

## 3. 数据模型设计

### 3.1 输入数据结构

#### Kafka Topic 数据格式
```json
{
  "id": "",
  "mn": "20160930A00013", 
  "pwd": "654321",
  "cn": "2061",
  "data_time": "20250731000000",
  "factors": {
    "011": {"factor_code": "011", "avg_value": "97.38", ...},
    "101": {"factor_code": "101", "avg_value": "198.53", ...},
    "B01": {"factor_code": "B01", "avg_value": "197.03", ...}
  }
}
```

#### 站点信息表结构
```sql
env.env_points_flink (
  point_id INT,
  mn_code STRING,
  point_type STRING, 
  operation_customer_id STRING,
  data_pwd STRING,
  gene_code_mapping STRING -- {"011": "NC68", "065": "NC92", ...}
)
```

### 3.2 输出数据结构

#### 目标表字段映射
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| src_points.point_id | point_id | 站点ID |
| src_t212_gf.data_time | data_time | 数据时间(转换格式) |
| src_t212_gf.cn | data_type | 数据类型 |
| src_t212_gf.id | qn | 请求编码 |
| src_t212_gf.mn | mncode | 设备唯一标识 |
| src_t212_gf.factors | gene_json | 因子数据JSON |
| 动态构建 | main_ext | 非固定字段JSON |
| 动态构建 | gene_trans_json | 因子编码映射JSON |

## 4. 核心功能设计

### 4.1 动态因子处理

#### 4.1.1 设计思路
- factors 字段包含动态的因子编码作为 key
- 需要与站点信息表中的 gene_code_mapping 进行映射
- 构建反向映射关系存储到 gene_trans_json

#### 4.1.2 处理流程
1. 解析 factors JSON 获取所有动态因子编码
2. 查询站点信息获取 gene_code_mapping
3. 构建反向映射：{mapped_value: original_key}
4. 生成 gene_trans_json 字段

### 4.2 分区表自动创建

#### 4.2.1 分区策略
- **2051 (分钟数据)**: 按月分区
- **2061 (小时数据)**: 按年分区  
- **2031 (日数据)**: 不做时间分区

#### 4.2.2 分区层级结构
```
env_2061_gf (父表)
├── env_2061_gf_31 (1级分区: point_type=31)
│   ├── env_2061_gf_31_2025 (2级分区: 2025年)
│   └── env_2061_gf_31_2026 (2级分区: 2026年)
└── env_2061_gf_32 (1级分区: point_type=32)
    ├── env_2061_gf_32_2025
    └── env_2061_gf_32_2026
```

#### 4.2.3 自动创建机制
1. **实时检查**: 数据写入前检查分区是否存在
2. **预创建**: 定时任务提前创建未来分区
3. **缓存机制**: 缓存已存在分区信息，避免重复检查
4. **批量创建**: 批量执行分区创建SQL，提高效率

### 4.3 多表路由写入

#### 4.3.1 路由策略
```
data_type = "2051" → env_2051_gf (分钟数据表)
data_type = "2061" → env_2061_gf (小时数据表)  
data_type = "2031" → env_2031_gf (日数据表)
```

#### 4.3.2 实现方案
1. **数据分流**: 使用 ProcessFunction 按 data_type 分流
2. **动态Sink**: 为每种数据类型创建专用的 JDBC Sink
3. **并行写入**: 不同数据流并行写入对应表
4. **冲突处理**: 使用 ON CONFLICT 处理重复数据

## 5. 技术实现方案

### 5.1 Flink 作业配置

#### 5.1.1 Kafka Source 配置
```sql
CREATE TABLE src_t212_gf (
    id STRING,
    mn STRING,
    pwd STRING,
    cn STRING,
    data_time STRING,
    factors STRING,  -- 存储完整JSON字符串
    -- 其他字段...
    proc_time AS PROCTIME()
) WITH (
    'connector' = 'kafka',
    'topic' = 't212_gf',
    'properties.bootstrap.servers' = 'kafka-cluster:9092',
    'format' = 'json'
);
```

#### 5.1.2 PostgreSQL Lookup 配置
```sql
CREATE TABLE src_points (
    point_id INT,
    mn_code STRING,
    point_type STRING,
    operation_customer_id STRING,
    data_pwd STRING,
    gene_code_mapping STRING,
    PRIMARY KEY (mn_code, data_pwd) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '***************************************',
    'table-name' = 'env.env_points_flink',
    'lookup.cache.max-rows' = '10000',
    'lookup.cache.ttl' = '1h'
);
```

### 5.2 自定义函数设计

#### 5.2.1 动态因子映射函数
- **功能**: 解析 factors JSON，构建 gene_trans_json
- **输入**: gene_code_mapping, factors JSON
- **输出**: 反向映射 JSON 字符串
- **异常处理**: 解析失败返回空JSON "{}"

#### 5.2.2 分区管理函数
- **功能**: 检查和创建分区表
- **输入**: data_type, point_type, data_time
- **输出**: 分区创建结果
- **缓存**: 使用本地缓存避免重复检查

### 5.3 数据处理流程

#### 5.3.1 主处理逻辑
```sql
CREATE VIEW join_t212_gf AS
SELECT 
    p.point_id,
    CAST(TO_TIMESTAMP(k.data_time, 'yyyyMMddHHmmss') AS TIMESTAMP) as data_time,
    k.cn as data_type,
    k.id as qn,
    k.mn as mncode,
    k.factors as gene_json,
    -- 构建 main_ext JSON
    JSON_OBJECT(
        'pwd', k.pwd,
        'outlet_code', k.outlet_code,
        -- 其他非固定字段...
    ) as main_ext,
    p.point_type,
    CURRENT_TIMESTAMP as write_time,
    'nt' as source,
    p.operation_customer_id,
    -- 使用自定义函数构建 gene_trans_json
    DynamicGeneTransMapping(p.gene_code_mapping, k.factors) as gene_trans_json
FROM src_t212_gf k
JOIN src_points FOR SYSTEM_TIME AS OF k.proc_time AS p
ON k.mn = p.mn_code AND k.pwd = p.data_pwd;
```

## 6. 性能优化设计

### 6.1 并行度配置（可参数化配置）
- **Source**: 1个并行度（可配置：parallelism.source）
- **处理**: 2个并行度（可配置：parallelism.process）
- **Sink**: 每个表1个并行度（可配置：parallelism.sink）
- **默认并行度**: 2（可配置：parallelism.default）

### 6.2 内存优化（小数据量场景）
- **Managed Memory**: 40% 用于状态存储
- **Network Memory**: 10% 用于网络缓冲
- **JVM Heap**: 50% 用于用户代码

### 6.3 批量写入优化（小数据量场景）
- **批次大小**: 100条记录（可配置：sink.batch.size）
- **批次间隔**: 10秒（可配置：sink.batch.interval）
- **连接池**: 每个Sink 2个连接（可配置：sink.connection.pool.size）
- **重试机制**: 最多重试3次

### 6.4 缓存策略（小数据量场景）
- **分区缓存**: 100个分区信息，30分钟过期（可配置）
- **Lookup缓存**: 1000个站点信息，30分钟过期（可配置）
- **连接缓存**: 复用数据库连接

## 7. 容错和监控

### 7.1 容错机制
- **Checkpoint**: 60秒间隔，exactly-once语义
- **重启策略**: 指数退避，最多重试10次
- **状态后端**: RocksDB，支持增量checkpoint

### 7.2 监控指标
- **数据处理量**: 每秒处理记录数
- **延迟监控**: 端到端处理延迟
- **错误率**: 解析失败、关联失败比例
- **分区创建**: 分区创建成功/失败次数
- **资源使用**: CPU、内存、网络使用率

### 7.3 告警规则
- 数据处理延迟 > 30秒
- 错误率 > 5%
- 分区创建失败率 > 10%
- 资源使用率 > 80%

## 8. 部署架构

### 8.1 集群配置（小数据量场景）
- **JobManager**: 1核2GB，单机模式
- **TaskManager**: 2核4GB，1个实例
- **总资源**: 3核6GB，并行度2

### 8.2 网络要求
- Kafka集群延迟 < 50ms（内网环境）
- PostgreSQL连接延迟 < 20ms
- 集群内网络带宽 > 100Mbps

### 8.3 存储要求
- Checkpoint存储: 普通磁盘，至少10GB
- 日志存储: 普通磁盘，至少10GB
- 状态存储: 本地磁盘，至少5GB

## 9. 配置参数说明

### 9.1 并行度配置参数
```properties
# 并行度配置（可根据数据量调整）
parallelism.default=2
parallelism.source=1
parallelism.process=2
parallelism.sink=1

# 小数据量建议配置
parallelism.default=1
parallelism.source=1
parallelism.process=1
parallelism.sink=1
```

### 9.2 缓存配置参数
```properties
# 分区缓存配置
partition.cache.max-size=100
partition.cache.expire-minutes=30

# Lookup缓存配置
lookup.cache.max-rows=1000
lookup.cache.ttl=30min
```

### 9.3 批量写入配置参数
```properties
# 批量写入配置
sink.batch.size=100
sink.batch.interval.seconds=10
sink.connection.pool.size=2
sink.connection.timeout.seconds=30
```

## 10. 风险评估

### 10.1 技术风险
- **分区创建失败**: 影响数据写入，通过日志监控
- **JSON解析异常**: 动态因子解析失败，记录错误日志
- **数据库连接**: 连接池配置合理，避免连接耗尽

### 10.2 业务风险
- **数据丢失**: 通过checkpoint和重试机制保障
- **数据重复**: 使用幂等写入机制
- **处理延迟**: 小数据量场景延迟可控

## 11. 总结

本设计方案针对小数据量场景进行了优化，通过 Flink 流处理技术实现国发数据的实时接入和处理。核心特点包括：

1. **参数化配置**: 并行度、缓存、批量写入等关键参数可配置
2. **动态处理**: 支持factors字段中动态因子编码的处理
3. **自动分区**: 实现PostgreSQL分区表的自动创建和管理
4. **多表路由**: 根据数据类型自动路由到不同目标表
5. **轻量化部署**: 针对小数据量优化资源配置

该方案适合小到中等数据量的国发数据接入场景，具备良好的可配置性和扩展性。
