package com.example.gf.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * Kafka中的国发数据模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class KafkaGfData {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("mn")
    private String mn;
    
    @JsonProperty("pwd")
    private String pwd;
    
    @JsonProperty("outlet_code")
    private String outletCode;
    
    @JsonProperty("cn")
    private String cn;
    
    @JsonProperty("data_time")
    private String dataTime;
    
    @JsonProperty("workcordSc")
    private String workcordSc;
    
    @JsonProperty("zd_workcordSc")
    private String zdWorkcordSc;
    
    @JsonProperty("zd_workcordZl")
    private String zdWorkcordZl;
    
    @JsonProperty("workcordZl")
    private String workcordZl;
    
    @JsonProperty("rg_workcordSc")
    private String rgWorkcordSc;
    
    @JsonProperty("rg_workcordZl")
    private String rgWorkcordZl;
    
    @JsonProperty("log_time")
    private String logTime;
    
    @JsonProperty("factors")
    private Map<String, Object> factors;
    
    // 默认构造函数
    public KafkaGfData() {}
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getMn() {
        return mn;
    }
    
    public void setMn(String mn) {
        this.mn = mn;
    }
    
    public String getPwd() {
        return pwd;
    }
    
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
    
    public String getOutletCode() {
        return outletCode;
    }
    
    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }
    
    public String getCn() {
        return cn;
    }
    
    public void setCn(String cn) {
        this.cn = cn;
    }
    
    public String getDataTime() {
        return dataTime;
    }
    
    public void setDataTime(String dataTime) {
        this.dataTime = dataTime;
    }
    
    public String getWorkcordSc() {
        return workcordSc;
    }
    
    public void setWorkcordSc(String workcordSc) {
        this.workcordSc = workcordSc;
    }
    
    public String getZdWorkcordSc() {
        return zdWorkcordSc;
    }
    
    public void setZdWorkcordSc(String zdWorkcordSc) {
        this.zdWorkcordSc = zdWorkcordSc;
    }
    
    public String getZdWorkcordZl() {
        return zdWorkcordZl;
    }
    
    public void setZdWorkcordZl(String zdWorkcordZl) {
        this.zdWorkcordZl = zdWorkcordZl;
    }
    
    public String getWorkcordZl() {
        return workcordZl;
    }
    
    public void setWorkcordZl(String workcordZl) {
        this.workcordZl = workcordZl;
    }
    
    public String getRgWorkcordSc() {
        return rgWorkcordSc;
    }
    
    public void setRgWorkcordSc(String rgWorkcordSc) {
        this.rgWorkcordSc = rgWorkcordSc;
    }
    
    public String getRgWorkcordZl() {
        return rgWorkcordZl;
    }
    
    public void setRgWorkcordZl(String rgWorkcordZl) {
        this.rgWorkcordZl = rgWorkcordZl;
    }
    
    public String getLogTime() {
        return logTime;
    }
    
    public void setLogTime(String logTime) {
        this.logTime = logTime;
    }
    
    public Map<String, Object> getFactors() {
        return factors;
    }
    
    public void setFactors(Map<String, Object> factors) {
        this.factors = factors;
    }
    
    @Override
    public String toString() {
        return "KafkaGfData{" +
                "id='" + id + '\'' +
                ", mn='" + mn + '\'' +
                ", pwd='" + pwd + '\'' +
                ", cn='" + cn + '\'' +
                ", dataTime='" + dataTime + '\'' +
                ", factors=" + factors +
                '}';
    }
}
