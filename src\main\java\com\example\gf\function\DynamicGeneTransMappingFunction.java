package com.example.gf.function;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 动态因子映射函数
 * 将 gene_code_mapping 和 factors 转换为 gene_trans_json
 */
@FunctionHint(output = @DataTypeHint("STRING"))
public class DynamicGeneTransMappingFunction extends ScalarFunction {
    
    private static final Logger LOG = LoggerFactory.getLogger(DynamicGeneTransMappingFunction.class);
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper以处理各种JSON格式
        this.objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        this.objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 构建动态因子映射
     * 
     * @param geneCodeMapping 因子编码映射 JSON，格式：{"011": "NC68", "065": "NC92", "101": "NC97", "B01": "NC137"}
     * @param factorsJson 因子数据 JSON，格式：{"011": {...}, "101": {...}, "B01": {...}, "065": {...}}
     * @return 反向映射 JSON，格式：{"NC68": "011", "NC92": "065", "NC97": "101", "NC137": "B01"}
     */
    public String eval(String geneCodeMapping, String factorsJson) {
        try {
            // 参数校验
            if (geneCodeMapping == null || factorsJson == null || 
                geneCodeMapping.trim().isEmpty() || factorsJson.trim().isEmpty()) {
                return "{}";
            }
            
            // 解析 gene_code_mapping
            Map<String, String> codeMapping = objectMapper.readValue(geneCodeMapping, 
                    new TypeReference<Map<String, String>>() {});
            
            if (codeMapping == null || codeMapping.isEmpty()) {
                return "{}";
            }
            
            // 解析 factors JSON 获取所有动态的 factor_code
            JsonNode factorsNode = objectMapper.readTree(factorsJson);
            
            if (factorsNode == null || !factorsNode.isObject()) {
                return "{}";
            }
            
            // 构建反向映射
            Map<String, String> reverseMapping = new HashMap<>();
            
            // 遍历 factors 中的所有 key
            Iterator<String> fieldNames = factorsNode.fieldNames();
            while (fieldNames.hasNext()) {
                String factorCode = fieldNames.next();
                
                // 在 gene_code_mapping 中查找对应的映射值
                String mappedValue = codeMapping.get(factorCode);
                if (mappedValue != null && !mappedValue.trim().isEmpty()) {
                    // 构建反向映射: {"NC68": "011", "NC92": "065", ...}
                    reverseMapping.put(mappedValue, factorCode);
                }
            }
            
            // 转换为JSON字符串
            String result = objectMapper.writeValueAsString(reverseMapping);
            
            LOG.debug("Gene trans mapping: geneCodeMapping={}, factorsKeys={}, result={}", 
                    geneCodeMapping, factorsNode.fieldNames(), result);
            
            return result;
            
        } catch (Exception e) {
            LOG.warn("Failed to build gene_trans_json mapping: geneCodeMapping={}, factorsJson={}", 
                    geneCodeMapping, factorsJson, e);
            return "{}";
        }
    }
    
    /**
     * 重载方法，支持空值处理
     */
    public String eval(String geneCodeMapping, String factorsJson, String defaultValue) {
        String result = eval(geneCodeMapping, factorsJson);
        return "{}".equals(result) ? (defaultValue != null ? defaultValue : "{}") : result;
    }
}
