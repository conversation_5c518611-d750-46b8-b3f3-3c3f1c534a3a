# 国发数据接入项目文档

## 项目概述

本项目使用 Apache Flink 1.17.2 从 Kafka 2.x 读取国发数据（topic=t212_gf），经过实时处理后写入 PostgreSQL 12 的分区表中。项目的核心挑战是处理动态的因子编码、自动创建分区表以及根据数据类型路由到不同目标表。

## 文档结构

### 📋 [国发数据接入设计方案.md](./国发数据接入设计方案.md)
**主设计文档** - 包含完整的系统架构和设计方案
- 系统架构图和数据流设计
- 核心功能设计（动态因子处理、分区管理、多表路由）
- 技术实现方案和性能优化
- 容错机制和监控体系
- 风险评估和总结

### 🔧 [技术实现细节.md](./技术实现细节.md)
**技术实现文档** - 详细的代码架构和实现方案
- 分区管理器详细设计
- 动态路由实现方案
- 自定义函数实现（动态因子映射、分区检查）
- 配置管理和监控指标设计
- 异常处理策略和性能调优参数

### 🗄️ [数据库设计.md](./数据库设计.md)
**数据库设计文档** - PostgreSQL分区表设计方案
- 源表和目标表结构设计
- 复合分区策略（LIST + RANGE）
- 索引设计和JSONB字段优化
- 分区管理策略和维护SQL
- 性能优化和监控查询

### 🚀 [部署运维指南.md](./部署运维指南.md)
**部署指南文档** - 针对小数据量场景的部署方案
- 环境准备和组件部署（小数据量优化）
- 作业配置和提交脚本（参数化配置）
- 基础监控和健康检查
- 简单故障处理和作业重启
- 性能调优（轻量化配置）

## 快速开始

### 1. 环境要求（小数据量场景优化）
- **Java**: 8+ (推荐2核4GB)
- **Flink**: 1.17.2 (推荐2核4GB)
- **Kafka**: 2.8.0+ (推荐2核4GB)
- **PostgreSQL**: 12+ (推荐4核8GB)
- **操作系统**: Linux (CentOS 7+)

### 2. 核心组件
```
Kafka Topic (t212_gf) 
    ↓
Flink Streaming Job
    ↓ (根据data_type路由)
PostgreSQL分区表
├── env_2051_gf (分钟数据)
├── env_2061_gf (小时数据)
└── env_2031_gf (日数据)
```

### 3. 关键特性
- ✅ **动态因子处理**: 支持factors字段中动态因子编码
- ✅ **自动分区管理**: PostgreSQL分区表自动创建和维护
- ✅ **多表路由**: 根据data_type自动路由到不同表
- ✅ **实时处理**: exactly-once语义保证数据一致性
- ✅ **高可用**: 完善的容错和监控机制

## 数据流处理

### 输入数据格式
```json
{
  "id": "",
  "mn": "20160930A00013",
  "pwd": "654321", 
  "cn": "2061",
  "data_time": "20250731000000",
  "factors": {
    "011": {"factor_code": "011", "avg_value": "97.38"},
    "101": {"factor_code": "101", "avg_value": "198.53"},
    "B01": {"factor_code": "B01", "avg_value": "197.03"}
  }
}
```

### 处理流程
1. **数据接入**: 从Kafka消费JSON格式数据
2. **数据关联**: 与站点信息表关联获取映射关系
3. **字段转换**: 构建目标表字段，包括动态因子映射
4. **分区检查**: 确保目标分区表存在
5. **数据路由**: 根据data_type路由到对应表
6. **批量写入**: 使用JDBC批量写入PostgreSQL

### 分区策略
| 数据类型 | 表名 | 一级分区 | 二级分区 | 说明 |
|---------|------|----------|----------|------|
| 2051 | env_2051_gf | point_type | 按月 | 分钟数据 |
| 2061 | env_2061_gf | point_type | 按年 | 小时数据 |
| 2031 | env_2031_gf | point_type | 无 | 日数据 |

## 核心技术方案

### 1. 动态因子映射
```java
// 自定义函数处理动态因子编码
DynamicGeneTransMapping(gene_code_mapping, factors)
// 输入: {"011": "NC68", "065": "NC92"}, {"011": {...}, "065": {...}}
// 输出: {"NC68": "011", "NC92": "065"}
```

### 2. 分区自动创建
```java
// 分区管理器确保分区存在
partitionManager.ensurePartitionExists(dataType, pointType, dataTime);
// 支持缓存机制，避免重复检查
```

### 3. 多表路由
```java
// 数据流分流器
switch (dataType) {
    case "2051": ctx.output(minuteDataTag, value); break;
    case "2061": ctx.output(hourDataTag, value); break;
    case "2031": ctx.output(dayDataTag, value); break;
}
```

## 性能指标

### 处理能力（小数据量场景）
- **吞吐量**: 支持1-10万条/小时
- **延迟**: 端到端延迟 < 60秒
- **可用性**: 99%以上

### 资源配置（小数据量优化）
- **JobManager**: 1核2GB
- **TaskManager**: 2核4GB × 1实例
- **并行度**: 2（可参数化配置：parallelism.default）

## 监控告警

### 基础监控指标（简化监控）
- Flink作业运行状态
- 数据处理基本统计
- 系统资源使用情况
- 分区创建状态

### 简单告警规则
- 作业停止运行 → 检查作业状态
- 处理延迟 > 60秒 → 检查数据量
- 连接失败 → 检查网络和配置

## 部署架构

```mermaid
graph TB
    subgraph "数据源"
        A[Kafka Cluster<br/>t212_gf topic]
        B[PostgreSQL<br/>env.env_points_flink]
    end
    
    subgraph "Flink集群（小数据量）"
        C[JobManager<br/>1核2GB]
        D[TaskManager<br/>2核4GB]
    end
    
    subgraph "目标存储"
        F[PostgreSQL<br/>env_2051_gf]
        G[PostgreSQL<br/>env_2061_gf]
        H[PostgreSQL<br/>env_2031_gf]
    end
    
    subgraph "监控系统"
        I[Prometheus]
        J[Grafana]
        K[AlertManager]
    end
    
    A --> D
    B --> D
    D --> F
    D --> G
    D --> H

    C --> I
    D --> I
    I --> J
    I --> K
```

## 配置管理

### 参数化配置
- **并行度配置**: parallelism.default/source/process/sink
- **缓存配置**: partition.cache.max-size, lookup.cache.max-rows
- **批量写入**: sink.batch.size, sink.batch.interval.seconds
- **连接池**: sink.connection.pool.size

### 简单维护
- **健康检查**: 基础脚本检查集群和作业状态
- **作业重启**: 简单的停止和重启脚本
- **分区检查**: 基础的分区存在性检查
- **配置调整**: 根据数据量调整关键参数

## 扩展性设计（参数化扩展）

### 配置扩展
- 调整并行度参数：parallelism.default=4
- 增加TaskManager资源：taskmanager.memory.process.size=8gb
- 调整批次参数：sink.batch.size=500

### 简单扩展步骤
1. 修改配置文件中的并行度参数
2. 重启Flink作业应用新配置
3. 监控处理性能和资源使用
4. 根据需要进一步调整参数

## 注意事项

### 开发注意事项
1. **动态字段处理**: factors字段中的因子编码是动态的，不能硬编码
2. **参数化配置**: 关键参数要支持配置文件修改，便于调优
3. **分区命名**: 严格按照命名规范创建分区表
4. **错误处理**: 完善的异常处理，避免单条错误数据影响整体处理

### 部署注意事项
1. **资源配置**: 根据实际数据量选择合适的资源配置
2. **参数调优**: 根据数据量和性能要求调整并行度和批次参数
3. **监控检查**: 定期检查作业状态和基础性能指标
4. **配置备份**: 重要配置文件要备份，便于快速恢复

## 联系信息

如有技术问题或需要支持，请联系项目团队：
- 架构设计: [架构师]
- 开发实现: [开发团队]
- 运维支持: [运维团队]

---

**文档版本**: v1.0  
**最后更新**: 2025-01-26  
**适用版本**: Flink 1.17.2, Kafka 2.x, PostgreSQL 12+
