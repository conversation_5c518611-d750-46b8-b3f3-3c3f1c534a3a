package com.example.gf.sink;

import com.example.gf.config.GfConfig;
import com.example.gf.model.GfTargetData;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态JDBC Sink工厂
 * 根据数据类型创建对应的JDBC Sink
 */
public class DynamicJdbcSinkFactory {
    
    private static final Logger LOG = LoggerFactory.getLogger(DynamicJdbcSinkFactory.class);
    
    // 表配置映射
    private static final Map<String, TableConfig> TABLE_CONFIGS = new HashMap<>();
    
    static {
        TABLE_CONFIGS.put("2051", new TableConfig("env_2051_gf", "分钟数据表"));
        TABLE_CONFIGS.put("2061", new TableConfig("env_2061_gf", "小时数据表"));
        TABLE_CONFIGS.put("2031", new TableConfig("env_2031_gf", "日数据表"));
    }
    
    /**
     * 创建JDBC Sink
     */
    public static SinkFunction<GfTargetData> createSink(String dataType, GfConfig config) {
        TableConfig tableConfig = TABLE_CONFIGS.get(dataType);
        if (tableConfig == null) {
            throw new IllegalArgumentException("Unsupported data type: " + dataType);
        }
        
        return JdbcSink.sink(
                generateInsertSQL(tableConfig.getTableName()),
                createJdbcStatementBuilder(),
                JdbcExecutionOptions.builder()
                        .withBatchSize(config.getSinkBatchSize())
                        .withBatchIntervalMs(config.getSinkBatchIntervalSeconds() * 1000L)
                        .withMaxRetries(3)
                        .build(),
                createJdbcConnectionOptions(config)
        );
    }
    
    /**
     * 生成插入SQL
     */
    private static String generateInsertSQL(String tableName) {
        return String.format(
                "INSERT INTO env.%s " +
                "(point_id, data_time, data_type, qn, st, mncode, flag, cp_ext, " +
                "gene_json, main_ext, auto_audit, ext_property, point_type, write_time, " +
                "source, operation_customer_id, gene_json_std, water_gene_label, " +
                "water_label, comm_rule, label_json, gene_trans_json) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, " +
                "?::jsonb, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb) " +
                "ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source) " +
                "DO UPDATE SET " +
                "gene_json = EXCLUDED.gene_json, " +
                "main_ext = EXCLUDED.main_ext, " +
                "gene_trans_json = EXCLUDED.gene_trans_json, " +
                "write_time = EXCLUDED.write_time",
                tableName
        );
    }
    
    /**
     * 创建JDBC语句构建器
     */
    private static JdbcStatementBuilder<GfTargetData> createJdbcStatementBuilder() {
        return new JdbcStatementBuilder<GfTargetData>() {
            @Override
            public void accept(PreparedStatement ps, GfTargetData data) throws SQLException {
                try {
                    int index = 1;
                    
                    // 基础字段
                    ps.setObject(index++, data.getPointId(), Types.INTEGER);
                    ps.setTimestamp(index++, data.getDataTime());
                    ps.setString(index++, data.getDataType());
                    ps.setString(index++, data.getQn());
                    ps.setString(index++, data.getSt());
                    ps.setString(index++, data.getMncode());
                    ps.setString(index++, data.getFlag());
                    
                    // JSONB字段
                    ps.setString(index++, data.getCpExt());
                    ps.setString(index++, data.getGeneJson());
                    ps.setString(index++, data.getMainExt());
                    ps.setString(index++, data.getAutoAudit());
                    ps.setString(index++, data.getExtProperty());
                    
                    // 其他字段
                    ps.setString(index++, data.getPointType());
                    ps.setTimestamp(index++, data.getWriteTime());
                    ps.setString(index++, data.getSource());
                    ps.setString(index++, data.getOperationCustomerId());
                    
                    // 扩展JSONB字段
                    ps.setString(index++, data.getGeneJsonStd());
                    ps.setString(index++, data.getWaterGeneLabel());
                    ps.setString(index++, data.getWaterLabel());
                    ps.setString(index++, data.getCommRule());
                    ps.setString(index++, data.getLabelJson());
                    ps.setString(index++, data.getGeneTransJson());
                    
                } catch (SQLException e) {
                    LOG.error("Failed to set parameters for data: {}", data, e);
                    throw e;
                }
            }
        };
    }
    
    /**
     * 创建JDBC连接选项
     */
    private static JdbcConnectionOptions createJdbcConnectionOptions(GfConfig config) {
        return new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                .withUrl(config.getPostgresqlUrl())
                .withDriverName(config.getPostgresqlDriver())
                .withUsername(config.getPostgresqlUsername())
                .withPassword(config.getPostgresqlPassword())
                .withConnectionCheckTimeoutSeconds(30)
                .build();
    }
    
    /**
     * 表配置类
     */
    public static class TableConfig {
        private final String tableName;
        private final String description;
        
        public TableConfig(String tableName, String description) {
            this.tableName = tableName;
            this.description = description;
        }
        
        public String getTableName() {
            return tableName;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
