package com.example.gf.partition;

import com.example.gf.config.GfConfig;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 分区管理器
 */
public class PartitionManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(PartitionManager.class);
    
    private final GfConfig config;
    private final Cache<String, Boolean> partitionCache;
    
    public PartitionManager(GfConfig config) {
        this.config = config;
        this.partitionCache = Caffeine.newBuilder()
                .maximumSize(config.getPartitionCacheMaxSize())
                .expireAfterWrite(config.getPartitionCacheExpireMinutes(), TimeUnit.MINUTES)
                .build();
    }
    
    /**
     * 确保分区存在
     */
    public void ensurePartitionExists(String dataType, String pointType, Timestamp dataTime) {
        String cacheKey = buildCacheKey(dataType, pointType, dataTime);
        
        // 检查缓存
        Boolean exists = partitionCache.getIfPresent(cacheKey);
        if (Boolean.TRUE.equals(exists)) {
            return;
        }
        
        try {
            // 检查并创建分区
            if (createPartitionIfNotExists(dataType, pointType, dataTime)) {
                partitionCache.put(cacheKey, true);
                LOG.info("Partition ensured for key: {}", cacheKey);
            }
        } catch (Exception e) {
            LOG.warn("Failed to ensure partition exists for key: {}", cacheKey, e);
        }
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String dataType, String pointType, Timestamp dataTime) {
        String timeSuffix = calculateTimeSuffix(dataType, dataTime);
        return String.format("%s_%s_%s", dataType, pointType, timeSuffix);
    }
    
    /**
     * 计算时间后缀
     */
    private String calculateTimeSuffix(String dataType, Timestamp dataTime) {
        LocalDateTime localDateTime = dataTime.toLocalDateTime();
        switch (dataType) {
            case "2051": // 分钟数据按月分区
                return localDateTime.format(DateTimeFormatter.ofPattern("yyyy_MM"));
            case "2061": // 小时数据按年分区
                return localDateTime.format(DateTimeFormatter.ofPattern("yyyy"));
            case "2031": // 日数据不做时间分区
            default:
                return "";
        }
    }
    
    /**
     * 创建分区（如果不存在）
     */
    private boolean createPartitionIfNotExists(String dataType, String pointType, Timestamp dataTime) {
        try (Connection conn = DriverManager.getConnection(
                config.getPostgresqlUrl(),
                config.getPostgresqlUsername(),
                config.getPostgresqlPassword())) {
            
            // 创建一级分区
            createLevel1Partition(conn, dataType, pointType);
            
            // 创建二级分区（如果需要）
            if (!"2031".equals(dataType)) {
                createLevel2Partition(conn, dataType, pointType, dataTime);
            }
            
            return true;
        } catch (SQLException e) {
            LOG.error("Failed to create partition for dataType={}, pointType={}, dataTime={}", 
                    dataType, pointType, dataTime, e);
            return false;
        }
    }
    
    /**
     * 创建一级分区
     */
    private void createLevel1Partition(Connection conn, String dataType, String pointType) throws SQLException {
        String tableName = String.format("env_%s_gf_%s", dataType, pointType);
        String parentTable = String.format("env_%s_gf", dataType);
        
        String partitionByClause = "";
        if (!"2031".equals(dataType)) {
            partitionByClause = "PARTITION BY RANGE (data_time)";
        }
        
        String sql = String.format(
                "CREATE TABLE IF NOT EXISTS env.%s " +
                "PARTITION OF env.%s " +
                "FOR VALUES IN ('%s') " +
                "%s",
                tableName, parentTable, pointType, partitionByClause
        );
        
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            LOG.debug("Created level 1 partition: {}", tableName);
        }
    }
    
    /**
     * 创建二级分区
     */
    private void createLevel2Partition(Connection conn, String dataType, String pointType, Timestamp dataTime) throws SQLException {
        String timeSuffix = calculateTimeSuffix(dataType, dataTime);
        String tableName = String.format("env_%s_gf_%s_%s", dataType, pointType, timeSuffix);
        String parentTable = String.format("env_%s_gf_%s", dataType, pointType);
        
        LocalDateTime localDateTime = dataTime.toLocalDateTime();
        String startTime, endTime;
        
        switch (dataType) {
            case "2051": // 按月分区
                startTime = localDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                endTime = localDateTime.withDayOfMonth(1).plusMonths(1).withHour(0).withMinute(0).withSecond(0)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                break;
            case "2061": // 按年分区
                startTime = localDateTime.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                endTime = localDateTime.withDayOfYear(1).plusYears(1).withHour(0).withMinute(0).withSecond(0)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                break;
            default:
                return;
        }
        
        String constraints = String.format(
                "CONSTRAINT %s_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)",
                tableName
        );
        
        String sql = String.format(
                "CREATE TABLE IF NOT EXISTS env.%s " +
                "PARTITION OF env.%s " +
                "(%s) " +
                "FOR VALUES FROM ('%s') TO ('%s')",
                tableName, parentTable, constraints, startTime, endTime
        );
        
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
            
            // 创建索引
            createPartitionIndexes(stmt, tableName);
            
            LOG.debug("Created level 2 partition: {}", tableName);
        }
    }
    
    /**
     * 创建分区索引
     */
    private void createPartitionIndexes(Statement stmt, String tableName) throws SQLException {
        // 时间索引
        String timeIndexSql = String.format(
                "CREATE INDEX IF NOT EXISTS idx_%s_dt ON env.%s USING btree (data_time)",
                tableName, tableName
        );
        stmt.execute(timeIndexSql);
        
        // 站点ID索引
        String pointIndexSql = String.format(
                "CREATE INDEX IF NOT EXISTS idx_%s_point_id ON env.%s USING btree (point_id)",
                tableName, tableName
        );
        stmt.execute(pointIndexSql);
        
        // MN码索引
        String mncodeIndexSql = String.format(
                "CREATE INDEX IF NOT EXISTS idx_%s_mncode ON env.%s USING btree (mncode)",
                tableName, tableName
        );
        stmt.execute(mncodeIndexSql);
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("PartitionCache stats: size=%d, hitRate=%.2f", 
                partitionCache.estimatedSize(),
                partitionCache.stats().hitRate());
    }
}
