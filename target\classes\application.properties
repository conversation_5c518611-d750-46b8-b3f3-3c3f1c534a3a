# Kafka配置
kafka.bootstrap.servers=localhost:9092
kafka.topic=t212_gf
kafka.group.id=flink-gf-consumer
kafka.auto.offset.reset=latest

# PostgreSQL配置（小数据量优化）
postgresql.url=*****************************************
postgresql.username=flink_user
postgresql.password=flink_password
postgresql.driver=org.postgresql.Driver
postgresql.connection.max-pool-size=2
postgresql.connection.timeout=30000

# 业务配置（可参数化）
parallelism.default=2
parallelism.source=1
parallelism.process=2
parallelism.sink=1

# 批量写入配置（小数据量优化）
sink.batch.size=100
sink.batch.interval.seconds=10

# 缓存配置（小数据量优化）
partition.cache.max-size=100
partition.cache.expire-minutes=30
lookup.cache.max-rows=1000
lookup.cache.ttl-minutes=30

# Checkpoint配置
checkpoint.interval.seconds=300
checkpoint.timeout.minutes=10

# 日志配置
logging.level=INFO
