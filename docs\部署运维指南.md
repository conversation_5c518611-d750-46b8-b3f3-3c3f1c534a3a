# 部署指南（小数据量场景）

## 1. 环境准备

### 1.1 基础环境要求（小数据量场景优化）

| 组件 | 版本要求 | 最小配置 | 推荐配置（小数据量） |
|------|----------|----------|----------|
| Java | 8+ | 1核2GB | 2核4GB |
| Flink | 1.17.2 | 1核2GB | 2核4GB |
| Kafka | 2.8.0+ | 1核2GB | 2核4GB |
| PostgreSQL | 12+ | 2核4GB | 4核8GB |
| 操作系统 | Linux | CentOS 7+ | CentOS 8+ |

### 1.2 网络要求（内网环境）
- Flink集群与Kafka集群延迟 < 50ms
- Flink集群与PostgreSQL延迟 < 20ms
- 集群内网络带宽 > 100Mbps
- 防火墙开放必要端口

### 1.3 存储要求（小数据量场景）
- **Checkpoint存储**: 普通磁盘，至少10GB
- **日志存储**: 普通磁盘，至少10GB
- **数据库存储**: 普通磁盘，根据数据量规划，建议预留30%空间

## 2. 组件部署

### 2.1 Flink集群部署

#### 2.1.1 集群配置文件

**flink-conf.yaml**:
```yaml
# JobManager配置（小数据量场景）
jobmanager.rpc.address: flink-jobmanager
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 2gb          # 减少内存配置
jobmanager.bind-host: 0.0.0.0

# TaskManager配置（小数据量场景）
taskmanager.memory.process.size: 4gb         # 减少内存配置
taskmanager.numberOfTaskSlots: 2             # 减少任务槽数量
taskmanager.bind-host: 0.0.0.0
taskmanager.rpc.port: 6122

# 简化部署 - 不使用高可用（小数据量场景）
# high-availability: none

# Checkpoint配置（本地存储）
state.backend: rocksdb
state.backend.incremental: true
state.checkpoints.dir: file:///opt/flink/checkpoints    # 本地存储
state.savepoints.dir: file:///opt/flink/savepoints      # 本地存储

# 执行配置（小数据量优化）
execution.checkpointing.mode: EXACTLY_ONCE
execution.checkpointing.interval: 300s       # 延长checkpoint间隔
execution.checkpointing.timeout: 10min
execution.checkpointing.max-concurrent-checkpoints: 1

# 重启策略（减少重试）
restart-strategy: exponential-delay
restart-strategy.exponential-delay.initial-backoff: 2s
restart-strategy.exponential-delay.max-backoff: 1min    # 减少最大退避时间
restart-strategy.exponential-delay.backoff-multiplier: 2.0
restart-strategy.exponential-delay.reset-backoff-threshold: 10min
restart-strategy.exponential-delay.jitter-factor: 0.1

# 网络配置（小数据量优化）
taskmanager.network.memory.fraction: 0.05    # 减少网络内存占用
taskmanager.network.memory.min: 32mb         # 减少最小网络内存
taskmanager.network.memory.max: 256mb        # 减少最大网络内存

# 其他配置（可参数化）
parallelism.default: 2                       # 默认并行度（可配置）
table.exec.resource.default-parallelism: 2   # 表执行并行度（可配置）
pipeline.object-reuse: true
```

#### 2.1.2 启动脚本

**start-cluster.sh**:
```bash
#!/bin/bash

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk
export FLINK_HOME=/opt/flink
export PATH=$FLINK_HOME/bin:$PATH

# 启动集群
echo "Starting Flink cluster..."
$FLINK_HOME/bin/start-cluster.sh

# 检查服务状态
sleep 10
if curl -f http://localhost:8081 > /dev/null 2>&1; then
    echo "Flink cluster started successfully"
else
    echo "Failed to start Flink cluster"
    exit 1
fi
```

### 2.2 依赖JAR包部署

#### 2.2.1 必需的连接器JAR包
```bash
# 下载并放置到 $FLINK_HOME/lib/ 目录
wget https://repo1.maven.org/maven2/org/apache/flink/flink-connector-kafka/1.17.2/flink-connector-kafka-1.17.2.jar
wget https://repo1.maven.org/maven2/org/apache/flink/flink-connector-jdbc/1.17.2/flink-connector-jdbc-1.17.2.jar
wget https://repo1.maven.org/maven2/org/postgresql/postgresql/42.5.0/postgresql-42.5.0.jar
wget https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.14.2/jackson-databind-2.14.2.jar
```

#### 2.2.2 自定义函数JAR包
```bash
# 编译并部署自定义函数
mvn clean package -DskipTests
cp target/gf-data-processor-1.0.jar $FLINK_HOME/lib/
```

### 2.3 数据库初始化

#### 2.3.1 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE env_data ENCODING 'UTF8';

-- 创建用户
CREATE USER flink_user WITH PASSWORD 'flink_password';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE env_data TO flink_user;
GRANT ALL ON SCHEMA env TO flink_user;
GRANT CREATE ON SCHEMA env TO flink_user;
```

#### 2.3.2 创建基础表结构
```bash
# 执行数据库初始化脚本
psql -h pg-host -U flink_user -d env_data -f scripts/init_database.sql
```

## 3. 作业部署

### 3.1 作业配置文件

**application.properties**（小数据量场景配置）:
```properties
# Kafka配置
kafka.bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
kafka.topic=t212_gf
kafka.group.id=flink-gf-consumer
kafka.auto.offset.reset=latest

# PostgreSQL配置（小数据量优化）
postgresql.url=***************************************
postgresql.username=flink_user
postgresql.password=flink_password
postgresql.driver=org.postgresql.Driver
postgresql.connection.max-pool-size=2         # 减少连接池大小
postgresql.connection.timeout=30000

# 业务配置（可参数化）
parallelism.default=2                         # 默认并行度
parallelism.source=1                          # 源并行度
parallelism.process=2                         # 处理并行度
parallelism.sink=1                            # 写入并行度

# 批量写入配置（小数据量优化）
batch.size=100                                # 减少批次大小
batch.interval.ms=10000                       # 增加批次间隔

# 缓存配置（小数据量优化）
partition.cache.max-size=100                  # 分区缓存大小
partition.cache.expire-minutes=30             # 分区缓存过期时间
lookup.cache.max-rows=1000                    # Lookup缓存大小
lookup.cache.ttl=30min                        # Lookup缓存TTL
```

### 3.2 作业提交脚本

**submit-job.sh**:
```bash
#!/bin/bash

FLINK_HOME=/opt/flink
JOB_JAR=gf-data-processor-1.0.jar
JOB_CLASS=com.example.GfDataProcessorJob
CONFIG_FILE=application.properties

# 检查Flink集群状态
if ! curl -f http://localhost:8081 > /dev/null 2>&1; then
    echo "Flink cluster is not running"
    exit 1
fi

# 提交作业
echo "Submitting Flink job..."
$FLINK_HOME/bin/flink run \
    --class $JOB_CLASS \
    --parallelism 4 \
    --detached \
    $JOB_JAR \
    --config-file $CONFIG_FILE

# 检查作业状态
sleep 5
JOB_ID=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[0].id')
if [ "$JOB_ID" != "null" ]; then
    echo "Job submitted successfully with ID: $JOB_ID"
else
    echo "Failed to submit job"
    exit 1
fi
```

### 3.3 作业监控脚本

**monitor-job.sh**:
```bash
#!/bin/bash

FLINK_WEB_UI=http://localhost:8081

# 获取作业列表
get_jobs() {
    curl -s $FLINK_WEB_UI/jobs | jq -r '.jobs[] | "\(.id) \(.name) \(.state)"'
}

# 获取作业详情
get_job_details() {
    local job_id=$1
    curl -s $FLINK_WEB_UI/jobs/$job_id | jq '.'
}

# 获取作业指标
get_job_metrics() {
    local job_id=$1
    curl -s $FLINK_WEB_UI/jobs/$job_id/metrics | jq '.'
}

# 主监控循环
while true; do
    echo "=== Flink Jobs Status ==="
    get_jobs
    echo ""
    
    # 检查是否有失败的作业
    failed_jobs=$(curl -s $FLINK_WEB_UI/jobs | jq -r '.jobs[] | select(.state=="FAILED") | .id')
    if [ -n "$failed_jobs" ]; then
        echo "WARNING: Found failed jobs: $failed_jobs"
        # 这里可以添加告警逻辑
    fi
    
    sleep 30
done
```

## 4. 监控告警

### 4.1 Prometheus监控配置

**prometheus.yml**:
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'flink'
    static_configs:
      - targets: ['flink-jobmanager:9249', 'flink-taskmanager:9249']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka1:9308', 'kafka2:9308', 'kafka3:9308']

  - job_name: 'postgresql'
    static_configs:
      - targets: ['pg-host:9187']
```

### 4.2 Grafana仪表板配置

**关键监控指标**:
- Flink作业状态和重启次数
- 数据处理吞吐量 (records/sec)
- 处理延迟 (end-to-end latency)
- Checkpoint成功率和耗时
- Kafka消费延迟 (consumer lag)
- PostgreSQL连接数和查询性能
- 系统资源使用率 (CPU/Memory/Disk)

### 4.3 告警规则配置

**alerting-rules.yml**:
```yaml
groups:
  - name: flink-alerts
    rules:
      - alert: FlinkJobDown
        expr: flink_jobmanager_numRunningJobs == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Flink job is down"
          
      - alert: FlinkHighLatency
        expr: flink_taskmanager_job_latency_source_id_operator_id_operator_subtask_index_latency{quantile="0.95"} > 30000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High processing latency detected"
          
      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag_sum > 10000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High Kafka consumer lag"
```

## 5. 基础监控

### 5.1 简单健康检查

**健康检查脚本 (health-check.sh)**:
```bash
#!/bin/bash

# 检查Flink集群
check_flink() {
    if curl -f http://localhost:8081 > /dev/null 2>&1; then
        echo "✓ Flink cluster is healthy"
    else
        echo "✗ Flink cluster is down"
        return 1
    fi
}

# 检查作业状态
check_job_status() {
    running_jobs=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[] | select(.state=="RUNNING") | .id' | wc -l)
    if [ $running_jobs -gt 0 ]; then
        echo "✓ $running_jobs Flink jobs are running"
    else
        echo "✗ No running Flink jobs found"
        return 1
    fi
}

# 执行检查
echo "=== Health Check Report ==="
check_flink && check_job_status
echo "=== Health Check Complete ==="
```

### 5.2 分区检查

**简单分区检查脚本**:
```bash
#!/bin/bash

DB_HOST=pg-host
DB_NAME=env_data
DB_USER=flink_user

# 检查分区是否存在
check_partitions() {
    echo "=== Partition Check ==="
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
        SELECT
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables
        WHERE schemaname = 'env'
          AND tablename LIKE 'env_%_gf_%'
        ORDER BY tablename;
    "
}

check_partitions
```

## 6. 简单故障处理

### 6.1 常见故障及解决方案

| 故障类型 | 症状 | 可能原因 | 解决方案 |
|---------|------|----------|----------|
| 作业停止 | 作业不运行 | 配置错误、资源不足 | 检查配置、重启作业 |
| 数据延迟 | 处理延迟增加 | 数据量突增 | 检查数据量、调整并行度 |
| 连接失败 | 无法连接数据库 | 网络问题、认证失败 | 检查网络、验证账号密码 |
| 分区错误 | 写入失败 | 分区不存在 | 手动创建分区 |

### 6.2 简单恢复流程

**作业重启脚本 (restart-job.sh)**:
```bash
#!/bin/bash

# 停止当前作业
stop_current_job() {
    job_id=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[] | select(.state=="RUNNING") | .id')
    if [ -n "$job_id" ]; then
        echo "Stopping job: $job_id"
        curl -X PATCH http://localhost:8081/jobs/$job_id?mode=cancel
        sleep 10
    fi
}

# 重新提交作业
restart_job() {
    echo "Restarting job..."
    ./submit-job.sh
}

# 执行重启
echo "Starting job restart..."
stop_current_job
restart_job
```

## 7. 性能调优（小数据量场景）

### 7.1 JVM调优参数（轻量化配置）

**flink-conf.yaml JVM配置**:
```yaml
env.java.opts: >
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -Xms1g
  -Xmx2g
```

### 7.2 资源调优建议（参数化配置）

**根据数据量调整配置**:
```yaml
# 极小数据量 (< 1万条/小时) - 推荐配置
parallelism.default: 1
parallelism.source: 1
parallelism.process: 1
parallelism.sink: 1
taskmanager.memory.process.size: 2gb
taskmanager.numberOfTaskSlots: 1

# 小数据量 (1-10万条/小时)
parallelism.default: 2
parallelism.source: 1
parallelism.process: 2
parallelism.sink: 1
taskmanager.memory.process.size: 4gb
taskmanager.numberOfTaskSlots: 2
```

## 8. 配置参数总结

### 8.1 关键可配置参数
```properties
# 并行度配置
parallelism.default=2
parallelism.source=1
parallelism.process=2
parallelism.sink=1

# 缓存配置
partition.cache.max-size=100
partition.cache.expire-minutes=30
lookup.cache.max-rows=1000
lookup.cache.ttl=30min

# 批量写入配置
sink.batch.size=100
sink.batch.interval.seconds=10
sink.connection.pool.size=2
```

这份部署指南针对小数据量场景进行了优化，提供了简化的部署和基础监控方案。
