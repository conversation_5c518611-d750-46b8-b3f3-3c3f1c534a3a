#!/bin/bash

# 国发数据处理系统健康检查脚本

# 配置参数
FLINK_WEB_UI="http://localhost:8081"
KAFKA_BROKER="localhost:9092"
PG_HOST="localhost"
PG_PORT="5432"
PG_DATABASE="env_data"
PG_USER="flink_user"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 打印带颜色的消息
print_status() {
    local status=$1
    local message=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✓${NC} $message"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}✗${NC} $message"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    else
        echo -e "${YELLOW}⚠${NC} $message"
    fi
}

# 检查Flink集群状态
check_flink_cluster() {
    echo "=== Checking Flink Cluster ==="
    
    # 检查Flink Web UI是否可访问
    if curl -f $FLINK_WEB_UI > /dev/null 2>&1; then
        print_status "PASS" "Flink Web UI is accessible at $FLINK_WEB_UI"
        
        # 获取集群概览信息
        CLUSTER_INFO=$(curl -s $FLINK_WEB_UI/overview)
        if [ $? -eq 0 ]; then
            TASKMANAGERS=$(echo $CLUSTER_INFO | grep -o '"taskmanagers":[0-9]*' | cut -d':' -f2)
            SLOTS_TOTAL=$(echo $CLUSTER_INFO | grep -o '"slots-total":[0-9]*' | cut -d':' -f2)
            SLOTS_AVAILABLE=$(echo $CLUSTER_INFO | grep -o '"slots-available":[0-9]*' | cut -d':' -f2)
            
            print_status "PASS" "TaskManagers: $TASKMANAGERS, Total Slots: $SLOTS_TOTAL, Available Slots: $SLOTS_AVAILABLE"
        fi
    else
        print_status "FAIL" "Flink Web UI is not accessible at $FLINK_WEB_UI"
        return 1
    fi
}

# 检查Flink作业状态
check_flink_jobs() {
    echo ""
    echo "=== Checking Flink Jobs ==="
    
    JOBS_RESPONSE=$(curl -s $FLINK_WEB_UI/jobs)
    if [ $? -eq 0 ]; then
        # 统计不同状态的作业数量
        RUNNING_JOBS=$(echo $JOBS_RESPONSE | grep -o '"state":"RUNNING"' | wc -l)
        FAILED_JOBS=$(echo $JOBS_RESPONSE | grep -o '"state":"FAILED"' | wc -l)
        CANCELED_JOBS=$(echo $JOBS_RESPONSE | grep -o '"state":"CANCELED"' | wc -l)
        
        if [ $RUNNING_JOBS -gt 0 ]; then
            print_status "PASS" "$RUNNING_JOBS job(s) are running"
        else
            print_status "FAIL" "No running jobs found"
        fi
        
        if [ $FAILED_JOBS -gt 0 ]; then
            print_status "FAIL" "$FAILED_JOBS job(s) have failed"
        fi
        
        if [ $CANCELED_JOBS -gt 0 ]; then
            print_status "WARN" "$CANCELED_JOBS job(s) are canceled"
        fi
        
        # 显示作业详细信息
        if command -v jq > /dev/null 2>&1; then
            echo $JOBS_RESPONSE | jq -r '.jobs[] | "Job: \(.name // "Unknown") - State: \(.state) - Duration: \(.duration // 0)ms"' 2>/dev/null
        fi
    else
        print_status "FAIL" "Unable to retrieve job information"
    fi
}

# 检查Kafka连接
check_kafka() {
    echo ""
    echo "=== Checking Kafka ==="
    
    # 检查Kafka是否可访问（需要kafka-topics.sh工具）
    if command -v kafka-topics.sh > /dev/null 2>&1; then
        if kafka-topics.sh --bootstrap-server $KAFKA_BROKER --list > /dev/null 2>&1; then
            print_status "PASS" "Kafka is accessible at $KAFKA_BROKER"
            
            # 检查t212_gf topic是否存在
            if kafka-topics.sh --bootstrap-server $KAFKA_BROKER --list | grep -q "t212_gf"; then
                print_status "PASS" "Topic 't212_gf' exists"
            else
                print_status "FAIL" "Topic 't212_gf' does not exist"
            fi
        else
            print_status "FAIL" "Kafka is not accessible at $KAFKA_BROKER"
        fi
    else
        print_status "WARN" "kafka-topics.sh not found, skipping Kafka check"
    fi
}

# 检查PostgreSQL连接
check_postgresql() {
    echo ""
    echo "=== Checking PostgreSQL ==="
    
    # 检查PostgreSQL是否可访问
    if command -v pg_isready > /dev/null 2>&1; then
        if pg_isready -h $PG_HOST -p $PG_PORT > /dev/null 2>&1; then
            print_status "PASS" "PostgreSQL is accessible at $PG_HOST:$PG_PORT"
            
            # 检查数据库连接
            if command -v psql > /dev/null 2>&1; then
                # 检查数据库是否存在
                DB_EXISTS=$(psql -h $PG_HOST -U $PG_USER -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$PG_DATABASE'" 2>/dev/null)
                if [ "$DB_EXISTS" = "1" ]; then
                    print_status "PASS" "Database '$PG_DATABASE' exists"
                    
                    # 检查关键表是否存在
                    TABLES_CHECK=$(psql -h $PG_HOST -U $PG_USER -d $PG_DATABASE -tAc "
                        SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_schema = 'env' 
                        AND table_name IN ('env_points_flink', 'env_2051_gf', 'env_2061_gf', 'env_2031_gf')
                    " 2>/dev/null)
                    
                    if [ "$TABLES_CHECK" = "4" ]; then
                        print_status "PASS" "All required tables exist"
                    else
                        print_status "FAIL" "Some required tables are missing (found $TABLES_CHECK/4)"
                    fi
                else
                    print_status "FAIL" "Database '$PG_DATABASE' does not exist"
                fi
            else
                print_status "WARN" "psql not found, skipping database checks"
            fi
        else
            print_status "FAIL" "PostgreSQL is not accessible at $PG_HOST:$PG_PORT"
        fi
    else
        print_status "WARN" "pg_isready not found, skipping PostgreSQL check"
    fi
}

# 检查系统资源
check_system_resources() {
    echo ""
    echo "=== Checking System Resources ==="
    
    # 检查内存使用率
    if command -v free > /dev/null 2>&1; then
        MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
            print_status "PASS" "Memory usage: ${MEMORY_USAGE}%"
        else
            print_status "WARN" "High memory usage: ${MEMORY_USAGE}%"
        fi
    fi
    
    # 检查磁盘使用率
    if command -v df > /dev/null 2>&1; then
        DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
        if [ $DISK_USAGE -lt 80 ]; then
            print_status "PASS" "Disk usage: ${DISK_USAGE}%"
        else
            print_status "WARN" "High disk usage: ${DISK_USAGE}%"
        fi
    fi
    
    # 检查CPU负载
    if command -v uptime > /dev/null 2>&1; then
        LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        print_status "PASS" "Load average: $LOAD_AVG"
    fi
}

# 生成健康检查报告
generate_report() {
    echo ""
    echo "=== Health Check Summary ==="
    echo "Total Checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$((TOTAL_CHECKS - PASSED_CHECKS - FAILED_CHECKS))${NC}"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "\n${GREEN}✓ Overall Status: HEALTHY${NC}"
        return 0
    else
        echo -e "\n${RED}✗ Overall Status: UNHEALTHY${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  --flink-ui URL      Flink Web UI URL (default: $FLINK_WEB_UI)"
    echo "  --kafka-broker HOST Kafka broker address (default: $KAFKA_BROKER)"
    echo "  --pg-host HOST      PostgreSQL host (default: $PG_HOST)"
    echo "  --pg-port PORT      PostgreSQL port (default: $PG_PORT)"
    echo "  --pg-database DB    PostgreSQL database (default: $PG_DATABASE)"
    echo "  --pg-user USER      PostgreSQL user (default: $PG_USER)"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --flink-ui)
            FLINK_WEB_UI="$2"
            shift 2
            ;;
        --kafka-broker)
            KAFKA_BROKER="$2"
            shift 2
            ;;
        --pg-host)
            PG_HOST="$2"
            shift 2
            ;;
        --pg-port)
            PG_PORT="$2"
            shift 2
            ;;
        --pg-database)
            PG_DATABASE="$2"
            shift 2
            ;;
        --pg-user)
            PG_USER="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    echo "=== GF Data Processor Health Check ==="
    echo "Timestamp: $(date)"
    echo ""
    
    check_flink_cluster
    check_flink_jobs
    check_kafka
    check_postgresql
    check_system_resources
    
    generate_report
}

# 执行主函数
main
