package com.example.gf.function;

import com.example.gf.config.GfConfig;
import com.example.gf.model.GfTargetData;
import com.example.gf.partition.PartitionManager;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 分区确保MapFunction
 * 在数据写入前确保分区存在
 */
public class PartitionEnsureMapFunction extends RichMapFunction<GfTargetData, GfTargetData> {
    
    private static final Logger LOG = LoggerFactory.getLogger(PartitionEnsureMapFunction.class);
    
    private final String dataType;
    private final GfConfig config;
    
    private transient PartitionManager partitionManager;
    
    public PartitionEnsureMapFunction(String dataType, GfConfig config) {
        this.dataType = dataType;
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.partitionManager = new PartitionManager(config);
        LOG.info("PartitionEnsureMapFunction opened for dataType: {}", dataType);
    }
    
    @Override
    public GfTargetData map(GfTargetData value) throws Exception {
        try {
            // 提取分区相关字段
            String pointType = value.getPointType();
            java.sql.Timestamp dataTime = value.getDataTime();
            
            // 确保分区存在
            partitionManager.ensurePartitionExists(dataType, pointType, dataTime);
            
            LOG.debug("Partition ensured for dataType={}, pointType={}, dataTime={}", 
                    dataType, pointType, dataTime);
            
            return value;
            
        } catch (Exception e) {
            LOG.warn("Failed to ensure partition exists for dataType={}, pointType={}, dataTime={}, continuing with data processing", 
                    dataType, value.getPointType(), value.getDataTime(), e);
            
            // 不阻断数据流，继续处理
            return value;
        }
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        if (partitionManager != null) {
            LOG.info("PartitionEnsureMapFunction closed. Cache stats: {}", 
                    partitionManager.getCacheStats());
        }
    }
}
