#!/bin/bash

# 国发数据处理作业重启脚本

# 配置参数
FLINK_WEB_UI="http://localhost:8081"
JOB_NAME_PATTERN="GF Data Processor Job"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查Flink集群状态
check_flink_cluster() {
    print_message $YELLOW "Checking Flink cluster status..."
    
    if curl -f $FLINK_WEB_UI > /dev/null 2>&1; then
        print_message $GREEN "✓ Flink cluster is running"
        return 0
    else
        print_message $RED "✗ Flink cluster is not accessible at $FLINK_WEB_UI"
        return 1
    fi
}

# 获取运行中的作业ID
get_running_jobs() {
    local jobs_response=$(curl -s $FLINK_WEB_UI/jobs)
    
    if [ $? -eq 0 ]; then
        # 如果有jq工具，使用jq解析
        if command -v jq > /dev/null 2>&1; then
            echo $jobs_response | jq -r '.jobs[] | select(.state=="RUNNING") | .id'
        else
            # 简单的grep解析（不够精确，但基本可用）
            echo $jobs_response | grep -o '"id":"[^"]*"' | grep -A1 '"state":"RUNNING"' | grep -o '[a-f0-9-]\{36\}'
        fi
    fi
}

# 停止指定作业
stop_job() {
    local job_id=$1
    
    print_message $YELLOW "Stopping job: $job_id"
    
    local response=$(curl -s -X PATCH "$FLINK_WEB_UI/jobs/$job_id?mode=cancel")
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✓ Stop command sent successfully"
        
        # 等待作业停止
        print_message $YELLOW "Waiting for job to stop..."
        local max_wait=30
        local wait_count=0
        
        while [ $wait_count -lt $max_wait ]; do
            local job_status=$(curl -s "$FLINK_WEB_UI/jobs/$job_id" | grep -o '"state":"[^"]*"' | cut -d'"' -f4)
            
            if [ "$job_status" = "CANCELED" ] || [ "$job_status" = "FINISHED" ] || [ "$job_status" = "FAILED" ]; then
                print_message $GREEN "✓ Job stopped with status: $job_status"
                return 0
            fi
            
            sleep 2
            wait_count=$((wait_count + 1))
            echo -n "."
        done
        
        echo ""
        print_message $YELLOW "⚠ Job stop timeout, but continuing with restart"
        return 0
    else
        print_message $RED "✗ Failed to stop job"
        return 1
    fi
}

# 停止所有运行中的作业
stop_all_jobs() {
    print_message $YELLOW "Looking for running jobs..."
    
    local running_jobs=$(get_running_jobs)
    
    if [ -z "$running_jobs" ]; then
        print_message $YELLOW "No running jobs found"
        return 0
    fi
    
    print_message $YELLOW "Found running jobs:"
    echo "$running_jobs"
    
    # 停止每个运行中的作业
    echo "$running_jobs" | while read -r job_id; do
        if [ -n "$job_id" ]; then
            stop_job "$job_id"
        fi
    done
    
    # 额外等待确保所有作业都停止
    sleep 5
}

# 启动新作业
start_job() {
    print_message $YELLOW "Starting new job..."
    
    # 检查提交脚本是否存在
    local submit_script="scripts/submit-job.sh"
    
    if [ -f "$submit_script" ]; then
        print_message $YELLOW "Using submit script: $submit_script"
        
        # 执行提交脚本
        bash "$submit_script"
        
        if [ $? -eq 0 ]; then
            print_message $GREEN "✓ Job started successfully"
            return 0
        else
            print_message $RED "✗ Failed to start job using submit script"
            return 1
        fi
    else
        print_message $RED "✗ Submit script not found: $submit_script"
        print_message $YELLOW "Please run the submit script manually or ensure it exists"
        return 1
    fi
}

# 验证作业启动
verify_job_startup() {
    print_message $YELLOW "Verifying job startup..."
    
    # 等待作业启动
    sleep 10
    
    local running_jobs=$(get_running_jobs)
    
    if [ -n "$running_jobs" ]; then
        local job_count=$(echo "$running_jobs" | wc -l)
        print_message $GREEN "✓ $job_count job(s) are now running"
        
        # 显示作业ID
        echo "$running_jobs" | while read -r job_id; do
            if [ -n "$job_id" ]; then
                print_message $GREEN "  Job ID: $job_id"
            fi
        done
        
        return 0
    else
        print_message $RED "✗ No jobs are running after restart"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  --flink-ui URL      Flink Web UI URL (default: $FLINK_WEB_UI)"
    echo "  --dry-run           Show what would be done without actually doing it"
    echo ""
    echo "This script will:"
    echo "  1. Check Flink cluster status"
    echo "  2. Stop all running jobs"
    echo "  3. Start a new job using submit-job.sh"
    echo "  4. Verify the job is running"
    echo ""
}

# 干运行模式
dry_run_mode() {
    print_message $YELLOW "=== DRY RUN MODE ==="
    print_message $YELLOW "The following actions would be performed:"
    print_message $YELLOW "1. Check Flink cluster at: $FLINK_WEB_UI"
    print_message $YELLOW "2. Stop all running jobs"
    print_message $YELLOW "3. Start new job using: scripts/submit-job.sh"
    print_message $YELLOW "4. Verify job startup"
    print_message $YELLOW "=== END DRY RUN ==="
}

# 解析命令行参数
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --flink-ui)
            FLINK_WEB_UI="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    print_message $GREEN "=== GF Data Processor Job Restart ==="
    print_message $YELLOW "Timestamp: $(date)"
    echo ""
    
    # 干运行模式
    if [ "$DRY_RUN" = true ]; then
        dry_run_mode
        exit 0
    fi
    
    # 检查Flink集群
    if ! check_flink_cluster; then
        print_message $RED "Cannot proceed without Flink cluster"
        exit 1
    fi
    
    echo ""
    
    # 停止所有运行中的作业
    stop_all_jobs
    
    echo ""
    
    # 启动新作业
    if start_job; then
        echo ""
        verify_job_startup
        
        if [ $? -eq 0 ]; then
            echo ""
            print_message $GREEN "=== Job Restart Completed Successfully ==="
            print_message $YELLOW "You can monitor the job at: $FLINK_WEB_UI"
        else
            echo ""
            print_message $RED "=== Job Restart Failed ==="
            print_message $YELLOW "Please check the logs and try again"
            exit 1
        fi
    else
        echo ""
        print_message $RED "=== Job Restart Failed ==="
        exit 1
    fi
}

# 执行主函数
main
