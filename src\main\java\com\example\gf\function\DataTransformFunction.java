package com.example.gf.function;

import com.example.gf.model.GfTargetData;
import com.example.gf.model.KafkaGfData;
import com.example.gf.model.PointInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据转换函数
 * 将 KafkaGfData 和 PointInfo 转换为 GfTargetData
 */
public class DataTransformFunction extends RichMapFunction<KafkaGfData, GfTargetData> {
    
    private static final Logger LOG = LoggerFactory.getLogger(DataTransformFunction.class);
    
    private transient ObjectMapper objectMapper;
    private transient DynamicGeneTransMappingFunction geneTransFunction;
    
    // 关联的站点信息
    private PointInfo pointInfo;
    
    public DataTransformFunction(PointInfo pointInfo) {
        this.pointInfo = pointInfo;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.objectMapper = new ObjectMapper();
        this.geneTransFunction = new DynamicGeneTransMappingFunction();
        this.geneTransFunction.open(parameters);
    }
    
    @Override
    public GfTargetData map(KafkaGfData kafkaData) throws Exception {
        try {
            GfTargetData targetData = new GfTargetData();
            
            // 基础字段映射
            targetData.setPointId(pointInfo.getPointId());
            targetData.setDataTime(parseDataTime(kafkaData.getDataTime()));
            targetData.setDataType(kafkaData.getCn());
            targetData.setQn(kafkaData.getId());
            targetData.setSt(""); // 当前没有
            targetData.setMncode(kafkaData.getMn());
            targetData.setFlag(""); // 默认为空
            targetData.setCpExt(null); // 指令参数CP，当前为空
            
            // 因子数据JSON
            String factorsJson = objectMapper.writeValueAsString(kafkaData.getFactors());
            targetData.setGeneJson(factorsJson);
            
            // 构建main_ext JSON
            targetData.setMainExt(buildMainExt(kafkaData));
            
            // 其他字段设置为空
            targetData.setAutoAudit(null);
            targetData.setExtProperty(null);
            
            // 站点相关字段
            targetData.setPointType(pointInfo.getPointType());
            targetData.setWriteTime(new Timestamp(System.currentTimeMillis()));
            targetData.setSource("nt");
            targetData.setOperationCustomerId(pointInfo.getOperationCustomerId());
            
            // 其他扩展字段设置为空
            targetData.setGeneJsonStd(null);
            targetData.setWaterGeneLabel(null);
            targetData.setWaterLabel(null);
            targetData.setCommRule(null);
            targetData.setLabelJson(null);
            
            // 构建gene_trans_json
            String geneTransJson = geneTransFunction.eval(
                    pointInfo.getGeneCodeMapping(), 
                    factorsJson
            );
            targetData.setGeneTransJson(geneTransJson);
            
            LOG.debug("Transformed data: pointId={}, dataType={}, mncode={}", 
                    targetData.getPointId(), targetData.getDataType(), targetData.getMncode());
            
            return targetData;
            
        } catch (Exception e) {
            LOG.error("Failed to transform data: kafkaData={}, pointInfo={}", 
                    kafkaData, pointInfo, e);
            throw e;
        }
    }
    
    /**
     * 解析数据时间
     */
    private Timestamp parseDataTime(String dataTimeStr) {
        try {
            if (dataTimeStr == null || dataTimeStr.trim().isEmpty()) {
                return new Timestamp(System.currentTimeMillis());
            }
            
            // 解析 yyyyMMddHHmmss 格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime localDateTime = LocalDateTime.parse(dataTimeStr, formatter);
            return Timestamp.valueOf(localDateTime);
            
        } catch (Exception e) {
            LOG.warn("Failed to parse data_time: {}, using current time", dataTimeStr, e);
            return new Timestamp(System.currentTimeMillis());
        }
    }
    
    /**
     * 构建main_ext JSON
     */
    private String buildMainExt(KafkaGfData kafkaData) {
        try {
            Map<String, Object> mainExt = new HashMap<>();
            
            // 添加非固定字段
            mainExt.put("pwd", kafkaData.getPwd());
            mainExt.put("outlet_code", kafkaData.getOutletCode());
            mainExt.put("workcordSc", kafkaData.getWorkcordSc());
            mainExt.put("zd_workcordSc", kafkaData.getZdWorkcordSc());
            mainExt.put("zd_workcordZl", kafkaData.getZdWorkcordZl());
            mainExt.put("workcordZl", kafkaData.getWorkcordZl());
            mainExt.put("rg_workcordSc", kafkaData.getRgWorkcordSc());
            mainExt.put("rg_workcordZl", kafkaData.getRgWorkcordZl());
            mainExt.put("log_time", kafkaData.getLogTime());
            
            return objectMapper.writeValueAsString(mainExt);
            
        } catch (Exception e) {
            LOG.warn("Failed to build main_ext JSON", e);
            return "{}";
        }
    }
}
