# 国发数据接入处理系统

基于 Apache Flink 1.17.2 的国发数据实时处理系统，从 Kafka 读取数据，经过处理后写入 PostgreSQL 分区表。

## 项目特性

- ✅ **动态因子处理**: 支持 factors 字段中动态因子编码的处理
- ✅ **自动分区管理**: PostgreSQL 分区表自动创建和维护
- ✅ **多表路由**: 根据 data_type 自动路由到不同表
- ✅ **参数化配置**: 关键参数支持配置文件调整
- ✅ **实时处理**: exactly-once 语义保证数据一致性
- ✅ **轻量化部署**: 针对小数据量场景优化

## 系统架构

```
Kafka Topic (t212_gf) 
    ↓
Flink Streaming Job
    ↓ (根据data_type路由)
PostgreSQL分区表
├── env_2051_gf (分钟数据)
├── env_2061_gf (小时数据)
└── env_2031_gf (日数据)
```

## 环境要求

| 组件 | 版本要求 | 推荐配置 |
|------|----------|----------|
| Java | 8+ | 2核4GB |
| Flink | 1.17.2 | 2核4GB |
| Kafka | 2.8.0+ | 2核4GB |
| PostgreSQL | 12+ | 4核8GB |
| Maven | 3.6+ | - |

## 快速开始

### 1. 编译项目

```bash
mvn clean package
```

### 2. 初始化数据库

```bash
psql -h localhost -U flink_user -d env_data -f scripts/init_database.sql
```

### 3. 配置参数

编辑 `src/main/resources/application.properties`：

```properties
# 基础配置
kafka.bootstrap.servers=localhost:9092
postgresql.url=*****************************************
postgresql.username=flink_user
postgresql.password=flink_password

# 并行度配置（可根据数据量调整）
parallelism.default=2
parallelism.source=1
parallelism.process=2
parallelism.sink=1

# 批量写入配置
sink.batch.size=100
sink.batch.interval.seconds=10

# 缓存配置
partition.cache.max-size=100
lookup.cache.max-rows=1000
```

### 4. 启动Flink集群

```bash
$FLINK_HOME/bin/start-cluster.sh
```

### 5. 提交作业

```bash
chmod +x scripts/submit-job.sh
./scripts/submit-job.sh
```

### 6. 监控作业

访问 Flink Web UI: http://localhost:8081

## 脚本说明

### 作业管理脚本

- **submit-job.sh**: 作业提交脚本，支持参数化配置
- **restart-job.sh**: 作业重启脚本，自动停止旧作业并启动新作业
- **health-check.sh**: 系统健康检查脚本

### 使用示例

```bash
# 提交作业
./scripts/submit-job.sh

# 健康检查
./scripts/health-check.sh

# 重启作业
./scripts/restart-job.sh

# 查看帮助
./scripts/submit-job.sh --help
```

## 配置参数说明

### 并行度配置

```properties
parallelism.default=2      # 默认并行度
parallelism.source=1       # 源并行度
parallelism.process=2      # 处理并行度
parallelism.sink=1         # 写入并行度
```

### 缓存配置

```properties
partition.cache.max-size=100           # 分区缓存大小
partition.cache.expire-minutes=30      # 分区缓存过期时间
lookup.cache.max-rows=1000             # Lookup缓存大小
lookup.cache.ttl-minutes=30            # Lookup缓存TTL
```

### 批量写入配置

```properties
sink.batch.size=100                    # 批次大小
sink.batch.interval.seconds=10         # 批次间隔
postgresql.connection.max-pool-size=2  # 连接池大小
```

## 数据流处理

### 输入数据格式

```json
{
  "id": "",
  "mn": "20160930A00013",
  "pwd": "654321",
  "cn": "2061",
  "data_time": "20250731000000",
  "factors": {
    "011": {"factor_code": "011", "avg_value": "97.38"},
    "101": {"factor_code": "101", "avg_value": "198.53"}
  }
}
```

### 处理流程

1. **数据接入**: 从Kafka消费JSON格式数据
2. **数据校验**: 校验必要字段完整性
3. **站点关联**: 与站点信息表关联获取映射关系
4. **字段转换**: 构建目标表字段，包括动态因子映射
5. **分区检查**: 确保目标分区表存在
6. **数据路由**: 根据data_type路由到对应表
7. **批量写入**: 使用JDBC批量写入PostgreSQL

### 分区策略

| 数据类型 | 表名 | 一级分区 | 二级分区 | 说明 |
|---------|------|----------|----------|------|
| 2051 | env_2051_gf | point_type | 按月 | 分钟数据 |
| 2061 | env_2061_gf | point_type | 按年 | 小时数据 |
| 2031 | env_2031_gf | point_type | 无 | 日数据 |

## 性能调优

### 根据数据量调整配置

```properties
# 极小数据量 (< 1万条/小时)
parallelism.default=1
sink.batch.size=50
sink.batch.interval.seconds=15

# 小数据量 (1-10万条/小时)
parallelism.default=2
sink.batch.size=100
sink.batch.interval.seconds=10

# 中等数据量 (10-100万条/小时)
parallelism.default=4
sink.batch.size=500
sink.batch.interval.seconds=5
```

## 监控和维护

### 健康检查

```bash
# 运行健康检查
./scripts/health-check.sh

# 检查特定组件
./scripts/health-check.sh --flink-ui http://flink:8081
```

### 日志查看

```bash
# Flink作业日志
tail -f $FLINK_HOME/log/flink-*-taskexecutor-*.log

# 应用日志
grep "GfDataProcessorJob" $FLINK_HOME/log/*.log
```

### 常见问题

1. **作业启动失败**
   - 检查Flink集群状态
   - 验证配置文件参数
   - 查看作业日志

2. **数据写入失败**
   - 检查PostgreSQL连接
   - 验证分区表是否存在
   - 检查数据格式

3. **性能问题**
   - 调整并行度配置
   - 优化批次大小和间隔
   - 检查资源使用情况

## 项目结构

```
├── src/main/java/com/example/gf/
│   ├── GfDataProcessorJob.java          # 主作业类
│   ├── config/GfConfig.java             # 配置管理
│   ├── model/                           # 数据模型
│   ├── function/                        # 自定义函数
│   ├── partition/                       # 分区管理
│   ├── service/                         # 业务服务
│   └── sink/                           # 数据写入
├── src/main/resources/
│   └── application.properties           # 配置文件
├── scripts/
│   ├── init_database.sql               # 数据库初始化
│   ├── submit-job.sh                   # 作业提交
│   ├── restart-job.sh                  # 作业重启
│   └── health-check.sh                 # 健康检查
├── docs/                               # 设计文档
├── pom.xml                             # Maven配置
└── README.md                           # 项目说明
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。
