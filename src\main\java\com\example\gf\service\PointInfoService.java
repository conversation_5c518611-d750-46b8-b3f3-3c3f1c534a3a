package com.example.gf.service;

import com.example.gf.config.GfConfig;
import com.example.gf.model.PointInfo;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.concurrent.TimeUnit;

/**
 * 站点信息查询服务
 */
public class PointInfoService {
    
    private static final Logger LOG = LoggerFactory.getLogger(PointInfoService.class);
    
    private final GfConfig config;
    private final Cache<String, PointInfo> pointInfoCache;
    
    public PointInfoService(GfConfig config) {
        this.config = config;
        this.pointInfoCache = Caffeine.newBuilder()
                .maximumSize(config.getLookupCacheMaxRows())
                .expireAfterWrite(config.getLookupCacheTtlMinutes(), TimeUnit.MINUTES)
                .build();
    }
    
    /**
     * 根据mn和pwd查询站点信息
     */
    public PointInfo getPointInfo(String mn, String pwd) {
        String cacheKey = buildCacheKey(mn, pwd);
        
        // 先从缓存获取
        PointInfo pointInfo = pointInfoCache.getIfPresent(cacheKey);
        if (pointInfo != null) {
            LOG.debug("Point info found in cache: mn={}, pwd={}", mn, pwd);
            return pointInfo;
        }
        
        // 从数据库查询
        pointInfo = queryPointInfoFromDatabase(mn, pwd);
        if (pointInfo != null) {
            pointInfoCache.put(cacheKey, pointInfo);
            LOG.debug("Point info loaded from database and cached: mn={}, pwd={}", mn, pwd);
        } else {
            LOG.warn("Point info not found: mn={}, pwd={}", mn, pwd);
        }
        
        return pointInfo;
    }
    
    /**
     * 从数据库查询站点信息
     */
    private PointInfo queryPointInfoFromDatabase(String mn, String pwd) {
        String sql = "SELECT point_id, mn_code, point_type, operation_customer_id, " +
                    "company_id, data_pwd, customer_id, data_flag, water_sampling_type, " +
                    "gene_code_mapping, write_time " +
                    "FROM env.env_points_flink " +
                    "WHERE mn_code = ? AND data_pwd = ?";
        
        try (Connection conn = DriverManager.getConnection(
                config.getPostgresqlUrl(),
                config.getPostgresqlUsername(),
                config.getPostgresqlPassword());
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setString(1, mn);
            ps.setString(2, pwd);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    PointInfo pointInfo = new PointInfo();
                    pointInfo.setPointId(rs.getInt("point_id"));
                    pointInfo.setMnCode(rs.getString("mn_code"));
                    pointInfo.setPointType(rs.getString("point_type"));
                    pointInfo.setOperationCustomerId(rs.getString("operation_customer_id"));
                    pointInfo.setCompanyId(rs.getString("company_id"));
                    pointInfo.setDataPwd(rs.getString("data_pwd"));
                    pointInfo.setCustomerId(rs.getString("customer_id"));
                    pointInfo.setDataFlag(rs.getString("data_flag"));
                    pointInfo.setWaterSamplingType(rs.getString("water_sampling_type"));
                    pointInfo.setGeneCodeMapping(rs.getString("gene_code_mapping"));
                    pointInfo.setWriteTime(rs.getTimestamp("write_time"));
                    
                    return pointInfo;
                }
            }
            
        } catch (SQLException e) {
            LOG.error("Failed to query point info: mn={}, pwd={}", mn, pwd, e);
        }
        
        return null;
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String mn, String pwd) {
        return mn + ":" + pwd;
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("PointInfoCache stats: size=%d, hitRate=%.2f", 
                pointInfoCache.estimatedSize(),
                pointInfoCache.stats().hitRate());
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        pointInfoCache.invalidateAll();
        LOG.info("Point info cache cleared");
    }
}
