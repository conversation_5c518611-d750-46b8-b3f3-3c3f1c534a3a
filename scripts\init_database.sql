-- 国发数据接入数据库初始化脚本

-- 创建schema
CREATE SCHEMA IF NOT EXISTS env;

-- 创建站点信息表
CREATE TABLE IF NOT EXISTS env.env_points_flink (
    point_id INTEGER NOT NULL,
    mn_code VARCHAR(50) NOT NULL,
    point_type VARCHAR(10) NOT NULL,
    operation_customer_id VARCHAR(32),
    company_id VARCHAR(32),
    data_pwd VARCHAR(20),
    customer_id VARCHAR(32),
    data_flag VARCHAR(50),
    water_sampling_type VARCHAR(10),
    gene_code_mapping TEXT,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT env_points_flink_pkey PRIMARY KEY (point_id),
    CONSTRAINT env_points_flink_mn_unique UNIQUE (mn_code, data_pwd)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_env_points_flink_mn_pwd ON env.env_points_flink (mn_code, data_pwd);
CREATE INDEX IF NOT EXISTS idx_env_points_flink_point_type ON env.env_points_flink (point_type);
CREATE INDEX IF NOT EXISTS idx_env_points_flink_operation_customer ON env.env_points_flink (operation_customer_id);

-- 表注释
COMMENT ON TABLE env.env_points_flink IS '站点信息基础表，用于Flink作业查询';
COMMENT ON COLUMN env.env_points_flink.gene_code_mapping IS '因子编码映射，JSON格式：{"011": "NC68", "065": "NC92"}';

-- 创建分钟数据父表
CREATE TABLE IF NOT EXISTS env.env_2051_gf (
    point_id INTEGER NOT NULL,
    data_time TIMESTAMP NOT NULL,
    data_type VARCHAR(10) NOT NULL,
    qn VARCHAR(30),
    st VARCHAR(10),
    mncode VARCHAR(50) NOT NULL,
    flag VARCHAR(10),
    cp_ext JSONB,
    gene_json JSONB,
    main_ext JSONB,
    auto_audit JSONB,
    ext_property JSONB,
    point_type VARCHAR(10) NOT NULL,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(30) DEFAULT 'nt',
    operation_customer_id VARCHAR(32),
    gene_json_std JSONB,
    water_gene_label JSONB,
    water_label JSONB,
    comm_rule JSONB,
    label_json JSONB,
    gene_trans_json JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT env_2051_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2051_gf IS '分钟报文正式表（国发）';
COMMENT ON COLUMN env.env_2051_gf.gene_trans_json IS '因子编码转换映射，格式：{"NC68": "011", "NC92": "065"}';

-- 创建小时数据父表
CREATE TABLE IF NOT EXISTS env.env_2061_gf (
    point_id INTEGER NOT NULL,
    data_time TIMESTAMP NOT NULL,
    data_type VARCHAR(10) NOT NULL,
    qn VARCHAR(30),
    st VARCHAR(10),
    mncode VARCHAR(50) NOT NULL,
    flag VARCHAR(10),
    cp_ext JSONB,
    gene_json JSONB,
    main_ext JSONB,
    auto_audit JSONB,
    ext_property JSONB,
    point_type VARCHAR(10) NOT NULL,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(30) DEFAULT 'nt',
    operation_customer_id VARCHAR(32),
    gene_json_std JSONB,
    water_gene_label JSONB,
    water_label JSONB,
    comm_rule JSONB,
    label_json JSONB,
    gene_trans_json JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT env_2061_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2061_gf IS '小时报文正式表（国发）';

-- 创建日数据父表
CREATE TABLE IF NOT EXISTS env.env_2031_gf (
    point_id INTEGER NOT NULL,
    data_time TIMESTAMP NOT NULL,
    data_type VARCHAR(10) NOT NULL,
    qn VARCHAR(30),
    st VARCHAR(10),
    mncode VARCHAR(50) NOT NULL,
    flag VARCHAR(10),
    cp_ext JSONB,
    gene_json JSONB,
    main_ext JSONB,
    auto_audit JSONB,
    ext_property JSONB,
    point_type VARCHAR(10) NOT NULL,
    write_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(30) DEFAULT 'nt',
    operation_customer_id VARCHAR(32),
    gene_json_std JSONB,
    water_gene_label JSONB,
    water_label JSONB,
    comm_rule JSONB,
    label_json JSONB,
    gene_trans_json JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT env_2031_gf_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) PARTITION BY LIST (point_type);

COMMENT ON TABLE env.env_2031_gf IS '日报文正式表（国发）';

-- 插入测试数据
INSERT INTO env.env_points_flink (
    point_id, mn_code, point_type, operation_customer_id, company_id, 
    data_pwd, customer_id, data_flag, water_sampling_type, gene_code_mapping
) VALUES (
    1321, '20160930A00013', '31', '1240664924649836544', '829',
    '654321', '1240664924649836544', 'env.data.flag.32', '2',
    '{"011": "NC68", "065": "NC92", "101": "NC97", "B01": "NC137"}'
) ON CONFLICT (point_id) DO NOTHING;

-- 创建示例分区表（point_type=31）
-- 分钟数据分区
CREATE TABLE IF NOT EXISTS env.env_2051_gf_31 PARTITION OF env.env_2051_gf 
FOR VALUES IN ('31')
PARTITION BY RANGE (data_time);

-- 小时数据分区
CREATE TABLE IF NOT EXISTS env.env_2061_gf_31 PARTITION OF env.env_2061_gf 
FOR VALUES IN ('31')
PARTITION BY RANGE (data_time);

-- 日数据分区（不做时间分区）
CREATE TABLE IF NOT EXISTS env.env_2031_gf_31 PARTITION OF env.env_2031_gf 
FOR VALUES IN ('31');

-- 为日数据分区创建索引
CREATE INDEX IF NOT EXISTS idx_env_2031_gf_31_dt ON env.env_2031_gf_31 USING btree (data_time);
CREATE INDEX IF NOT EXISTS idx_env_2031_gf_31_point_id ON env.env_2031_gf_31 USING btree (point_id);
CREATE INDEX IF NOT EXISTS idx_env_2031_gf_31_mncode ON env.env_2031_gf_31 USING btree (mncode);

-- 创建当前年月的分区表示例
-- 分钟数据当前月分区
CREATE TABLE IF NOT EXISTS env.env_2051_gf_31_2025_01 PARTITION OF env.env_2051_gf_31 (
    CONSTRAINT env_2051_gf_31_2025_01_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2025-02-01 00:00:00');

CREATE INDEX IF NOT EXISTS idx_env_2051_gf_31_2025_01_dt ON env.env_2051_gf_31_2025_01 USING btree (data_time);
CREATE INDEX IF NOT EXISTS idx_env_2051_gf_31_2025_01_point_id ON env.env_2051_gf_31_2025_01 USING btree (point_id);

-- 小时数据当前年分区
CREATE TABLE IF NOT EXISTS env.env_2061_gf_31_2025 PARTITION OF env.env_2061_gf_31 (
    CONSTRAINT env_2061_gf_31_2025_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2026-01-01 00:00:00');

CREATE INDEX IF NOT EXISTS idx_env_2061_gf_31_2025_dt ON env.env_2061_gf_31_2025 USING btree (data_time);
CREATE INDEX IF NOT EXISTS idx_env_2061_gf_31_2025_point_id ON env.env_2061_gf_31_2025 USING btree (point_id);

-- 创建错误记录表（可选）
CREATE TABLE IF NOT EXISTS env.error_records (
    id SERIAL PRIMARY KEY,
    error_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_type VARCHAR(50),
    mn VARCHAR(50),
    raw_data TEXT,
    error_message TEXT
);

COMMENT ON TABLE env.error_records IS '错误数据记录表';

-- 输出初始化完成信息
SELECT 'Database initialization completed successfully' as status;
