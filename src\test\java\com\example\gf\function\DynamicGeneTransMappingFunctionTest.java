package com.example.gf.function;

import org.apache.flink.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 动态因子映射函数测试
 */
public class DynamicGeneTransMappingFunctionTest {
    
    private DynamicGeneTransMappingFunction function;
    
    @Before
    public void setUp() throws Exception {
        function = new DynamicGeneTransMappingFunction();
        function.open(new Configuration());
    }
    
    @Test
    public void testNormalMapping() {
        String geneCodeMapping = "{\"011\": \"NC68\", \"065\": \"NC92\", \"101\": \"NC97\", \"B01\": \"NC137\"}";
        String factorsJson = "{\"011\": {\"factor_code\": \"011\", \"avg_value\": \"97.38\"}, " +
                            "\"101\": {\"factor_code\": \"101\", \"avg_value\": \"198.53\"}, " +
                            "\"B01\": {\"factor_code\": \"B01\", \"avg_value\": \"197.03\"}}";
        
        String result = function.eval(geneCodeMapping, factorsJson);
        
        assertNotNull(result);
        assertNotEquals("{}", result);
        
        // 结果应该包含反向映射
        assertTrue(result.contains("NC68"));
        assertTrue(result.contains("NC97"));
        assertTrue(result.contains("NC137"));
        assertTrue(result.contains("011"));
        assertTrue(result.contains("101"));
        assertTrue(result.contains("B01"));
    }
    
    @Test
    public void testEmptyGeneCodeMapping() {
        String geneCodeMapping = "{}";
        String factorsJson = "{\"011\": {\"factor_code\": \"011\", \"avg_value\": \"97.38\"}}";
        
        String result = function.eval(geneCodeMapping, factorsJson);
        
        assertEquals("{}", result);
    }
    
    @Test
    public void testEmptyFactors() {
        String geneCodeMapping = "{\"011\": \"NC68\"}";
        String factorsJson = "{}";
        
        String result = function.eval(geneCodeMapping, factorsJson);
        
        assertEquals("{}", result);
    }
    
    @Test
    public void testNullInputs() {
        String result1 = function.eval(null, null);
        assertEquals("{}", result1);
        
        String result2 = function.eval(null, "{}");
        assertEquals("{}", result2);
        
        String result3 = function.eval("{}", null);
        assertEquals("{}", result3);
    }
    
    @Test
    public void testInvalidJson() {
        String geneCodeMapping = "invalid json";
        String factorsJson = "{\"011\": {\"factor_code\": \"011\"}}";
        
        String result = function.eval(geneCodeMapping, factorsJson);
        
        assertEquals("{}", result);
    }
    
    @Test
    public void testPartialMapping() {
        // gene_code_mapping 中只有部分因子的映射
        String geneCodeMapping = "{\"011\": \"NC68\", \"101\": \"NC97\"}";
        String factorsJson = "{\"011\": {\"factor_code\": \"011\"}, " +
                            "\"101\": {\"factor_code\": \"101\"}, " +
                            "\"B01\": {\"factor_code\": \"B01\"}}"; // B01没有映射
        
        String result = function.eval(geneCodeMapping, factorsJson);
        
        assertNotNull(result);
        assertNotEquals("{}", result);
        
        // 应该只包含有映射的因子
        assertTrue(result.contains("NC68"));
        assertTrue(result.contains("NC97"));
        assertTrue(result.contains("011"));
        assertTrue(result.contains("101"));
        
        // 不应该包含没有映射的因子
        assertFalse(result.contains("B01"));
    }
    
    @Test
    public void testWithDefaultValue() {
        String geneCodeMapping = "{}";
        String factorsJson = "{\"011\": {\"factor_code\": \"011\"}}";
        String defaultValue = "{\"default\": \"mapping\"}";
        
        String result = function.eval(geneCodeMapping, factorsJson, defaultValue);
        
        assertEquals(defaultValue, result);
    }
}
