使用flink从kafka 读取 topic=t212_gf（国发数据），结果写入到PG表 env_{data_type}_gf

## kafka topic=t212_gf（国发数据）

**说明 
- cn为data_type，代表2051:分钟数据，2061:小时数据，2031:日数据
- data_time时间格式为yyyyMMddHHmiss

**数据样例 
``` json
{"id":"","mn":"20160930A00013","pwd":"654321","outlet_code":"","cn":"2061","data_time":"20250731000000","workcordSc":"","zd_workcordSc":"","zd_workcordZl":"","workcordZl":"","rg_workcordSc":"","rg_workcordZl":"","log_time":"","factors":{"011":{"factor_code":"011","rtd_value":"","avg_value":"97.38","min_value":"","max_value":"","cou_value":"","avg_hm_revised":"","avg_revised":"","cou_zs_revised":"","rtd_zs_value":"","avg_zs_value":"","min_zs_value":"","max_zs_value":"","cou_zs_value":"","avg_zs_hm_revised":"","avg_zs_revised":"","flag":"","zd_flag":"","rg_flag":"","value_revised":"","cou_value_revised":"","outlet_standard":"","outlet_standard_cou":"","zd_data_valid":"","rg_data_valid":"","value_unit":"","cou_value_unit":""},"101":{"factor_code":"101","rtd_value":"","avg_value":"198.53","min_value":"","max_value":"","cou_value":"","avg_hm_revised":"","avg_revised":"","cou_zs_revised":"","rtd_zs_value":"","avg_zs_value":"","min_zs_value":"","max_zs_value":"","cou_zs_value":"","avg_zs_hm_revised":"","avg_zs_revised":"","flag":"","zd_flag":"","rg_flag":"","value_revised":"","cou_value_revised":"","outlet_standard":"","outlet_standard_cou":"","zd_data_valid":"","rg_data_valid":"","value_unit":"","cou_value_unit":""},"B01":{"factor_code":"B01","rtd_value":"","avg_value":"197.03","min_value":"","max_value":"","cou_value":"","avg_hm_revised":"","avg_revised":"","cou_zs_revised":"","rtd_zs_value":"","avg_zs_value":"","min_zs_value":"","max_zs_value":"","cou_zs_value":"","avg_zs_hm_revised":"","avg_zs_revised":"","flag":"","zd_flag":"","rg_flag":"","value_revised":"","cou_value_revised":"","outlet_standard":"","outlet_standard_cou":"","zd_data_valid":"","rg_data_valid":"","value_unit":"","cou_value_unit":""},"065":{"factor_code":"065","rtd_value":"","avg_value":"164.53","min_value":"","max_value":"","cou_value":"","avg_hm_revised":"","avg_revised":"","cou_zs_revised":"","rtd_zs_value":"","avg_zs_value":"","min_zs_value":"","max_zs_value":"","cou_zs_value":"","avg_zs_hm_revised":"","avg_zs_revised":"","flag":"","zd_flag":"","rg_flag":"","value_revised":"","cou_value_revised":"","outlet_standard":"","outlet_standard_cou":"","zd_data_valid":"","rg_data_valid":"","value_unit":"","cou_value_unit":""}}}
```

## pg库站点信息基础信息表env.env_points_flink
**说明 
- 字段"gene_code_mapping"代表因子编码映射

**数据样例 
``` 
"point_id","mn_code","point_type","operation_customer_id","company_id","data_pwd","customer_id","data_flag","water_sampling_type","gene_code_mapping","write_time"
"1321","20160930A00013","32","1240664924649836544","829","123456","1240664924649836544",env.data.flag.32,"2","{""011"": ""NC68"", ""065"": ""NC92"", ""101"": ""NC97"", ""B01"": ""NC137""}",2025-04-08 17:13:42.811
```

## pg库国发数据结果表env.env_{data_type}_gf
**说明 
- 字段"gene_code_mapping"代表因子编码映射
- 复合分区表，1级分区字段为point_type。2级分区字段，当2051:分钟数据时，data_time按月份分区；当2061:小时数据时，data_time按年分区；当2031:日数据时，data_time不做分区。

以2061:小时数据为例：
**父表定义
```
CREATE TABLE env.env_2061_gf (
	point_id int4 NULL, -- 站点ID
	data_time timestamp NULL, -- 数据时间
	data_type varchar(10) NULL, -- 数据类型，对应cn:数据类别
	qn varchar(30) NULL, -- 请求编码，对应id:唯一标识
	st varchar(10) NULL, -- 系统编码，当前没有
	mncode varchar(50) NULL, -- 设备唯一标识,对应mn:设备mn码
	flag varchar(10) NULL, -- 拆分包及应答标志
	cp_ext jsonb NULL, -- 指令参数CP中的非因子监测数据jsonb格式
	gene_json jsonb NULL,
	main_ext jsonb NULL, -- 数据段中非固定字段的数据jsonb格式，包含outlet_code:排放口编码,
	auto_audit jsonb NULL, -- 汇聚最新的自动审核质控数据，如值为0，恒值，超标等，客户自定义规则不做更新自动审核标识valid为1:有效,0:无效,2:存疑
	ext_property jsonb NULL, -- 报文附加属性, jsonb格式
	point_type varchar(10) NULL, -- 站点类型
	write_time timestamp DEFAULT now() NULL, -- 报文写入时间
	"source" varchar(30) NULL, -- 地表水自动监测系统数据传输规范（广东省DB 44_T 2028-2017）,来源标识：平台标识 nt;移动端标识 mt;基站命令 hd;数据删除标识 DEL;人工质控/数据录入 mo
	operation_customer_id varchar(32) NULL, -- 运维企业对应的客户
	gene_json_std jsonb NULL, -- 记录浓度和排放量的单位和换算率，如果换算率不为1，需要进行浓度和排放量标准化
	water_gene_label jsonb NULL, -- 水质因子标签，只有当point_type=21地表水标记，目前记录水质因子的cwqi和级别
	water_label jsonb NULL, -- 水质报文标签，只有当point_type=21地表水标记，目前记录水质报文的cwqi和级别
	comm_rule jsonb NULL, -- 通用异常标签计算：206:超量程下限,205:超量程上限,216:超检出限下限,215:超检出限上限,201:值为空,203:值为负,202:值为0
	label_json jsonb NULL,
	gene_trans_json jsonb DEFAULT '{}'::jsonb NULL
)
PARTITION BY LIST (point_type);
COMMENT ON TABLE env.env_2061_gf IS '小时报文正式表（国发）';
```

**1级分区表定义

```
CREATE TABLE env.env_2061_gf_31 PARTITION OF env.env_2061_gf FOR VALUES IN ('31')

PARTITION BY RANGE (data_time);

COMMENT ON TABLE env.env_2061_gf_31 IS '小时数据正式表（国发）';
```

**2级分区表定义
```
CREATE TABLE env.env_2061_gf_31_2025 PARTITION OF env.env_2061_gf_31 (
	CONSTRAINT env_2061_gf_31_2025_pkey PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)
) FOR VALUES FROM ('2025-01-01 00:00:00') TO ('2026-01-01 00:00:00');
CREATE INDEX idx_env_2061_gf_31_2025_dt ON env.env_2061_gf_31_2025 USING btree (data_time);
```


## 实现步骤
- 1、使用flink从kafka 读取 topic=t212_gf（国发数据），数据格式是json结构，可以通过json解析，记录为src_t212_gf
- 2、使用flink从pg库读取站点信息基础信息表env.env_points_flink，记录为src_points
- 3、两个数据源进行关联 ，关联条件为  src_t212_gf.mn=src_points.mn_code  and src_t212_gf.pwd=src_points.data_pwd，关联后的数据集为join_t212_gf，字段映射如下

| 源字段                                                                                                       | 目标字段                  |     |
| --------------------------------------------------------------------------------------------------------- | --------------------- | --- |
| src_points.point_id                                                                                       | point_id              |     |
| src_t212_gf.data_time                                                                                     | data_time             |     |
| src_t212_gf.cn                                                                                            | data_type             |     |
| src_t212_gf.id                                                                                            | qn                    |     |
| src_t212_gf.mn                                                                                            | mncode                |     |
| src_t212_gf.factors                                                                                       | gene_json             |     |
| 记录数据源src_t212_gf中除data_time、cn、id、mn、factors的属性，以json格式记录                                                 | main_ext              |     |
| src_points.point_type                                                                                     | point_type            |     |
| now()                                                                                                     | write_time            |     |
| nt                                                                                                        | source                |     |
| src_points.operation_customer_id                                                                          | operation_customer_id |     |
| 遍历src_t212_gf.factors的每个key，检索key在src_points."gene_code_mapping"对应的value值，以{value1:key1,value2:key2...}记录 | gene_trans_json       |     |

目标字段示例：
**"main_ext"**:{"pwd":"654321","outlet_code":"","workcordSc":"","zd_workcordSc":"","zd_workcordZl":"","workcordZl":"","rg_workcordSc":"","rg_workcordZl":""}

**"gene_trans_json"**:
{"NC68": "011", "NC92": "065", "NC97": "101", "NC137": "B01"}"

- 4、join_t212_gf写pg库国发数据结果表env.env_{data_type}_gf