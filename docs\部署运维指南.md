# 部署运维指南

## 1. 环境准备

### 1.1 基础环境要求

| 组件 | 版本要求 | 最小配置 | 推荐配置 |
|------|----------|----------|----------|
| Java | 8+ | 2核4GB | 4核8GB |
| Flink | 1.17.2 | 2核4GB | 4核8GB |
| Kafka | 2.8.0+ | 2核4GB | 4核8GB |
| PostgreSQL | 12+ | 4核8GB | 8核16GB |
| 操作系统 | Linux | CentOS 7+ | CentOS 8+ |

### 1.2 网络要求
- Flink集群与Kafka集群延迟 < 10ms
- Flink集群与PostgreSQL延迟 < 5ms
- 集群内网络带宽 > 1Gbps
- 防火墙开放必要端口

### 1.3 存储要求
- **Checkpoint存储**: SSD，至少100GB，IOPS > 3000
- **日志存储**: 普通磁盘，至少50GB
- **数据库存储**: SSD，根据数据量规划，建议预留50%空间

## 2. 组件部署

### 2.1 Flink集群部署

#### 2.1.1 集群配置文件

**flink-conf.yaml**:
```yaml
# JobManager配置
jobmanager.rpc.address: flink-jobmanager
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 4gb
jobmanager.bind-host: 0.0.0.0

# TaskManager配置  
taskmanager.memory.process.size: 8gb
taskmanager.numberOfTaskSlots: 4
taskmanager.bind-host: 0.0.0.0
taskmanager.rpc.port: 6122

# 高可用配置
high-availability: zookeeper
high-availability.zookeeper.quorum: zk1:2181,zk2:2181,zk3:2181
high-availability.storageDir: hdfs://namenode:port/flink/ha
high-availability.cluster-id: /flink-cluster

# Checkpoint配置
state.backend: rocksdb
state.backend.incremental: true
state.checkpoints.dir: hdfs://namenode:port/flink-checkpoints
state.savepoints.dir: hdfs://namenode:port/flink-savepoints

# 执行配置
execution.checkpointing.mode: EXACTLY_ONCE
execution.checkpointing.interval: 60s
execution.checkpointing.timeout: 10min
execution.checkpointing.max-concurrent-checkpoints: 1

# 重启策略
restart-strategy: exponential-delay
restart-strategy.exponential-delay.initial-backoff: 2s
restart-strategy.exponential-delay.max-backoff: 2min
restart-strategy.exponential-delay.backoff-multiplier: 2.0
restart-strategy.exponential-delay.reset-backoff-threshold: 10min
restart-strategy.exponential-delay.jitter-factor: 0.1

# 网络配置
taskmanager.network.memory.fraction: 0.1
taskmanager.network.memory.min: 64mb
taskmanager.network.memory.max: 1gb

# 其他配置
parallelism.default: 4
table.exec.resource.default-parallelism: 4
pipeline.object-reuse: true
```

#### 2.1.2 启动脚本

**start-cluster.sh**:
```bash
#!/bin/bash

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk
export FLINK_HOME=/opt/flink
export PATH=$FLINK_HOME/bin:$PATH

# 启动集群
echo "Starting Flink cluster..."
$FLINK_HOME/bin/start-cluster.sh

# 检查服务状态
sleep 10
if curl -f http://localhost:8081 > /dev/null 2>&1; then
    echo "Flink cluster started successfully"
else
    echo "Failed to start Flink cluster"
    exit 1
fi
```

### 2.2 依赖JAR包部署

#### 2.2.1 必需的连接器JAR包
```bash
# 下载并放置到 $FLINK_HOME/lib/ 目录
wget https://repo1.maven.org/maven2/org/apache/flink/flink-connector-kafka/1.17.2/flink-connector-kafka-1.17.2.jar
wget https://repo1.maven.org/maven2/org/apache/flink/flink-connector-jdbc/1.17.2/flink-connector-jdbc-1.17.2.jar
wget https://repo1.maven.org/maven2/org/postgresql/postgresql/42.5.0/postgresql-42.5.0.jar
wget https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.14.2/jackson-databind-2.14.2.jar
```

#### 2.2.2 自定义函数JAR包
```bash
# 编译并部署自定义函数
mvn clean package -DskipTests
cp target/gf-data-processor-1.0.jar $FLINK_HOME/lib/
```

### 2.3 数据库初始化

#### 2.3.1 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE env_data ENCODING 'UTF8';

-- 创建用户
CREATE USER flink_user WITH PASSWORD 'flink_password';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE env_data TO flink_user;
GRANT ALL ON SCHEMA env TO flink_user;
GRANT CREATE ON SCHEMA env TO flink_user;
```

#### 2.3.2 创建基础表结构
```bash
# 执行数据库初始化脚本
psql -h pg-host -U flink_user -d env_data -f scripts/init_database.sql
```

## 3. 作业部署

### 3.1 作业配置文件

**application.properties**:
```properties
# Kafka配置
kafka.bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
kafka.topic=t212_gf
kafka.group.id=flink-gf-consumer
kafka.auto.offset.reset=latest

# PostgreSQL配置
postgresql.url=***************************************
postgresql.username=flink_user
postgresql.password=flink_password
postgresql.driver=org.postgresql.Driver
postgresql.connection.max-pool-size=10
postgresql.connection.timeout=30000

# 业务配置
parallelism.source=2
parallelism.process=4
parallelism.sink=2
batch.size=1000
batch.interval.ms=5000
```

### 3.2 作业提交脚本

**submit-job.sh**:
```bash
#!/bin/bash

FLINK_HOME=/opt/flink
JOB_JAR=gf-data-processor-1.0.jar
JOB_CLASS=com.example.GfDataProcessorJob
CONFIG_FILE=application.properties

# 检查Flink集群状态
if ! curl -f http://localhost:8081 > /dev/null 2>&1; then
    echo "Flink cluster is not running"
    exit 1
fi

# 提交作业
echo "Submitting Flink job..."
$FLINK_HOME/bin/flink run \
    --class $JOB_CLASS \
    --parallelism 4 \
    --detached \
    $JOB_JAR \
    --config-file $CONFIG_FILE

# 检查作业状态
sleep 5
JOB_ID=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[0].id')
if [ "$JOB_ID" != "null" ]; then
    echo "Job submitted successfully with ID: $JOB_ID"
else
    echo "Failed to submit job"
    exit 1
fi
```

### 3.3 作业监控脚本

**monitor-job.sh**:
```bash
#!/bin/bash

FLINK_WEB_UI=http://localhost:8081

# 获取作业列表
get_jobs() {
    curl -s $FLINK_WEB_UI/jobs | jq -r '.jobs[] | "\(.id) \(.name) \(.state)"'
}

# 获取作业详情
get_job_details() {
    local job_id=$1
    curl -s $FLINK_WEB_UI/jobs/$job_id | jq '.'
}

# 获取作业指标
get_job_metrics() {
    local job_id=$1
    curl -s $FLINK_WEB_UI/jobs/$job_id/metrics | jq '.'
}

# 主监控循环
while true; do
    echo "=== Flink Jobs Status ==="
    get_jobs
    echo ""
    
    # 检查是否有失败的作业
    failed_jobs=$(curl -s $FLINK_WEB_UI/jobs | jq -r '.jobs[] | select(.state=="FAILED") | .id')
    if [ -n "$failed_jobs" ]; then
        echo "WARNING: Found failed jobs: $failed_jobs"
        # 这里可以添加告警逻辑
    fi
    
    sleep 30
done
```

## 4. 监控告警

### 4.1 Prometheus监控配置

**prometheus.yml**:
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'flink'
    static_configs:
      - targets: ['flink-jobmanager:9249', 'flink-taskmanager:9249']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka1:9308', 'kafka2:9308', 'kafka3:9308']

  - job_name: 'postgresql'
    static_configs:
      - targets: ['pg-host:9187']
```

### 4.2 Grafana仪表板配置

**关键监控指标**:
- Flink作业状态和重启次数
- 数据处理吞吐量 (records/sec)
- 处理延迟 (end-to-end latency)
- Checkpoint成功率和耗时
- Kafka消费延迟 (consumer lag)
- PostgreSQL连接数和查询性能
- 系统资源使用率 (CPU/Memory/Disk)

### 4.3 告警规则配置

**alerting-rules.yml**:
```yaml
groups:
  - name: flink-alerts
    rules:
      - alert: FlinkJobDown
        expr: flink_jobmanager_numRunningJobs == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Flink job is down"
          
      - alert: FlinkHighLatency
        expr: flink_taskmanager_job_latency_source_id_operator_id_operator_subtask_index_latency{quantile="0.95"} > 30000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High processing latency detected"
          
      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag_sum > 10000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High Kafka consumer lag"
```

## 5. 运维操作

### 5.1 日常运维检查

**健康检查脚本 (health-check.sh)**:
```bash
#!/bin/bash

# 检查Flink集群
check_flink() {
    if curl -f http://localhost:8081 > /dev/null 2>&1; then
        echo "✓ Flink cluster is healthy"
    else
        echo "✗ Flink cluster is down"
        return 1
    fi
}

# 检查Kafka连接
check_kafka() {
    if kafka-topics.sh --bootstrap-server kafka1:9092 --list > /dev/null 2>&1; then
        echo "✓ Kafka is accessible"
    else
        echo "✗ Kafka is not accessible"
        return 1
    fi
}

# 检查PostgreSQL连接
check_postgresql() {
    if pg_isready -h pg-host -p 5432 > /dev/null 2>&1; then
        echo "✓ PostgreSQL is accessible"
    else
        echo "✗ PostgreSQL is not accessible"
        return 1
    fi
}

# 检查作业状态
check_job_status() {
    running_jobs=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[] | select(.state=="RUNNING") | .id' | wc -l)
    if [ $running_jobs -gt 0 ]; then
        echo "✓ $running_jobs Flink jobs are running"
    else
        echo "✗ No running Flink jobs found"
        return 1
    fi
}

# 执行所有检查
echo "=== Health Check Report ==="
check_flink && check_kafka && check_postgresql && check_job_status
echo "=== Health Check Complete ==="
```

### 5.2 分区管理

**分区维护脚本 (partition-maintenance.sh)**:
```bash
#!/bin/bash

DB_HOST=pg-host
DB_NAME=env_data
DB_USER=flink_user

# 创建未来分区
create_future_partitions() {
    local data_type=$1
    local months_ahead=$2
    
    for i in $(seq 1 $months_ahead); do
        if [ "$data_type" = "2051" ]; then
            # 分钟数据按月创建
            target_date=$(date -d "+$i month" +%Y-%m)
            psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
                SELECT create_monthly_partition('$data_type', '$target_date');
            "
        elif [ "$data_type" = "2061" ]; then
            # 小时数据按年创建
            target_date=$(date -d "+$i year" +%Y)
            psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
                SELECT create_yearly_partition('$data_type', '$target_date');
            "
        fi
    done
}

# 清理旧分区
cleanup_old_partitions() {
    local data_type=$1
    local retention_months=$2
    
    cutoff_date=$(date -d "-$retention_months month" +%Y-%m-%d)
    
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
        SELECT drop_old_partitions('$data_type', '$cutoff_date');
    "
}

# 执行分区维护
echo "Creating future partitions..."
create_future_partitions "2051" 3  # 分钟数据提前3个月
create_future_partitions "2061" 1  # 小时数据提前1年

echo "Cleaning up old partitions..."
cleanup_old_partitions "2051" 6   # 分钟数据保留6个月
cleanup_old_partitions "2061" 24  # 小时数据保留2年

echo "Partition maintenance completed"
```

### 5.3 数据质量检查

**数据质量检查脚本 (data-quality-check.sh)**:
```bash
#!/bin/bash

DB_HOST=pg-host
DB_NAME=env_data
DB_USER=flink_user

# 检查数据完整性
check_data_integrity() {
    echo "=== Data Integrity Check ==="
    
    # 检查最近1小时的数据量
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
        SELECT 
            data_type,
            COUNT(*) as record_count,
            MIN(data_time) as earliest_time,
            MAX(data_time) as latest_time
        FROM (
            SELECT data_type, data_time FROM env.env_2051_gf WHERE data_time >= NOW() - INTERVAL '1 hour'
            UNION ALL
            SELECT data_type, data_time FROM env.env_2061_gf WHERE data_time >= NOW() - INTERVAL '1 hour'
            UNION ALL  
            SELECT data_type, data_time FROM env.env_2031_gf WHERE data_time >= NOW() - INTERVAL '1 hour'
        ) t
        GROUP BY data_type
        ORDER BY data_type;
    "
}

# 检查数据质量
check_data_quality() {
    echo "=== Data Quality Check ==="
    
    # 检查空值比例
    psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
        SELECT 
            'env_2061_gf' as table_name,
            COUNT(*) as total_records,
            COUNT(*) - COUNT(gene_json) as null_gene_json,
            COUNT(*) - COUNT(gene_trans_json) as null_gene_trans_json,
            ROUND((COUNT(*) - COUNT(gene_json)) * 100.0 / COUNT(*), 2) as null_gene_json_pct
        FROM env.env_2061_gf 
        WHERE data_time >= CURRENT_DATE;
    "
}

# 执行检查
check_data_integrity
check_data_quality
```

## 6. 故障处理

### 6.1 常见故障及解决方案

| 故障类型 | 症状 | 可能原因 | 解决方案 |
|---------|------|----------|----------|
| 作业重启 | 频繁重启 | 内存不足、网络问题 | 增加内存、检查网络 |
| 数据延迟 | 处理延迟增加 | 资源不足、分区问题 | 扩容、优化分区 |
| 连接失败 | 无法连接数据库 | 连接池耗尽、网络问题 | 调整连接池、检查网络 |
| 分区错误 | 写入失败 | 分区不存在 | 手动创建分区 |

### 6.2 紧急恢复流程

**作业恢复脚本 (emergency-recovery.sh)**:
```bash
#!/bin/bash

# 停止当前作业
stop_current_job() {
    job_id=$(curl -s http://localhost:8081/jobs | jq -r '.jobs[] | select(.state=="RUNNING") | .id')
    if [ -n "$job_id" ]; then
        echo "Stopping job: $job_id"
        curl -X PATCH http://localhost:8081/jobs/$job_id?mode=cancel
        sleep 10
    fi
}

# 从最新savepoint恢复
restore_from_savepoint() {
    local savepoint_path=$1
    echo "Restoring from savepoint: $savepoint_path"
    
    $FLINK_HOME/bin/flink run \
        --class com.example.GfDataProcessorJob \
        --fromSavepoint $savepoint_path \
        --allowNonRestoredState \
        gf-data-processor-1.0.jar
}

# 执行恢复
echo "Starting emergency recovery..."
stop_current_job

# 查找最新的savepoint
latest_savepoint=$(hdfs dfs -ls /flink-savepoints | tail -1 | awk '{print $8}')
if [ -n "$latest_savepoint" ]; then
    restore_from_savepoint $latest_savepoint
else
    echo "No savepoint found, starting fresh job"
    ./submit-job.sh
fi
```

## 7. 性能调优

### 7.1 JVM调优参数

**flink-conf.yaml JVM配置**:
```yaml
env.java.opts: >
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+UnlockExperimentalVMOptions
  -XX:+UseCGroupMemoryLimitForHeap
  -XX:+PrintGCDetails
  -XX:+PrintGCTimeStamps
  -Xloggc:/opt/flink/log/gc.log
  -XX:+UseGCLogFileRotation
  -XX:NumberOfGCLogFiles=10
  -XX:GCLogFileSize=10M
```

### 7.2 资源调优建议

**根据数据量调整配置**:
```yaml
# 小数据量 (< 10万条/小时)
parallelism.default: 2
taskmanager.memory.process.size: 4gb
taskmanager.numberOfTaskSlots: 2

# 中等数据量 (10-100万条/小时)  
parallelism.default: 4
taskmanager.memory.process.size: 8gb
taskmanager.numberOfTaskSlots: 4

# 大数据量 (> 100万条/小时)
parallelism.default: 8
taskmanager.memory.process.size: 16gb
taskmanager.numberOfTaskSlots: 8
```

这份部署运维指南提供了完整的部署、监控、维护和故障处理方案，确保系统的稳定运行。
