# 国发数据接入项目文档

## 项目概述

本项目使用 Apache Flink 1.17.2 从 Kafka 2.x 读取国发数据（topic=t212_gf），经过实时处理后写入 PostgreSQL 12 的分区表中。项目的核心挑战是处理动态的因子编码、自动创建分区表以及根据数据类型路由到不同目标表。

## 文档结构

### 📋 [国发数据接入设计方案.md](./国发数据接入设计方案.md)
**主设计文档** - 包含完整的系统架构和设计方案
- 系统架构图和数据流设计
- 核心功能设计（动态因子处理、分区管理、多表路由）
- 技术实现方案和性能优化
- 容错机制和监控体系
- 风险评估和总结

### 🔧 [技术实现细节.md](./技术实现细节.md)
**技术实现文档** - 详细的代码架构和实现方案
- 分区管理器详细设计
- 动态路由实现方案
- 自定义函数实现（动态因子映射、分区检查）
- 配置管理和监控指标设计
- 异常处理策略和性能调优参数

### 🗄️ [数据库设计.md](./数据库设计.md)
**数据库设计文档** - PostgreSQL分区表设计方案
- 源表和目标表结构设计
- 复合分区策略（LIST + RANGE）
- 索引设计和JSONB字段优化
- 分区管理策略和维护SQL
- 性能优化和监控查询

### 🚀 [部署运维指南.md](./部署运维指南.md)
**部署运维文档** - 完整的部署和运维方案
- 环境准备和组件部署
- 作业配置和提交脚本
- 监控告警配置（Prometheus + Grafana）
- 日常运维检查和故障处理
- 性能调优和紧急恢复流程

## 快速开始

### 1. 环境要求
- **Java**: 8+
- **Flink**: 1.17.2
- **Kafka**: 2.8.0+
- **PostgreSQL**: 12+
- **操作系统**: Linux (CentOS 7+)

### 2. 核心组件
```
Kafka Topic (t212_gf) 
    ↓
Flink Streaming Job
    ↓ (根据data_type路由)
PostgreSQL分区表
├── env_2051_gf (分钟数据)
├── env_2061_gf (小时数据)
└── env_2031_gf (日数据)
```

### 3. 关键特性
- ✅ **动态因子处理**: 支持factors字段中动态因子编码
- ✅ **自动分区管理**: PostgreSQL分区表自动创建和维护
- ✅ **多表路由**: 根据data_type自动路由到不同表
- ✅ **实时处理**: exactly-once语义保证数据一致性
- ✅ **高可用**: 完善的容错和监控机制

## 数据流处理

### 输入数据格式
```json
{
  "id": "",
  "mn": "20160930A00013",
  "pwd": "654321", 
  "cn": "2061",
  "data_time": "20250731000000",
  "factors": {
    "011": {"factor_code": "011", "avg_value": "97.38"},
    "101": {"factor_code": "101", "avg_value": "198.53"},
    "B01": {"factor_code": "B01", "avg_value": "197.03"}
  }
}
```

### 处理流程
1. **数据接入**: 从Kafka消费JSON格式数据
2. **数据关联**: 与站点信息表关联获取映射关系
3. **字段转换**: 构建目标表字段，包括动态因子映射
4. **分区检查**: 确保目标分区表存在
5. **数据路由**: 根据data_type路由到对应表
6. **批量写入**: 使用JDBC批量写入PostgreSQL

### 分区策略
| 数据类型 | 表名 | 一级分区 | 二级分区 | 说明 |
|---------|------|----------|----------|------|
| 2051 | env_2051_gf | point_type | 按月 | 分钟数据 |
| 2061 | env_2061_gf | point_type | 按年 | 小时数据 |
| 2031 | env_2031_gf | point_type | 无 | 日数据 |

## 核心技术方案

### 1. 动态因子映射
```java
// 自定义函数处理动态因子编码
DynamicGeneTransMapping(gene_code_mapping, factors)
// 输入: {"011": "NC68", "065": "NC92"}, {"011": {...}, "065": {...}}
// 输出: {"NC68": "011", "NC92": "065"}
```

### 2. 分区自动创建
```java
// 分区管理器确保分区存在
partitionManager.ensurePartitionExists(dataType, pointType, dataTime);
// 支持缓存机制，避免重复检查
```

### 3. 多表路由
```java
// 数据流分流器
switch (dataType) {
    case "2051": ctx.output(minuteDataTag, value); break;
    case "2061": ctx.output(hourDataTag, value); break;
    case "2031": ctx.output(dayDataTag, value); break;
}
```

## 性能指标

### 处理能力
- **吞吐量**: 支持10万条/小时以上
- **延迟**: 端到端延迟 < 30秒
- **可用性**: 99.9%以上

### 资源配置
- **JobManager**: 2核4GB
- **TaskManager**: 4核8GB × 2实例
- **并行度**: 8（可根据数据量调整）

## 监控告警

### 关键监控指标
- 数据处理吞吐量和延迟
- Flink作业状态和重启次数
- Kafka消费延迟
- PostgreSQL连接池状态
- 分区创建成功率
- 系统资源使用率

### 告警规则
- 作业停止运行 → 立即告警
- 处理延迟 > 30秒 → 警告
- 错误率 > 5% → 警告
- 资源使用率 > 80% → 警告

## 部署架构

```mermaid
graph TB
    subgraph "数据源"
        A[Kafka Cluster<br/>t212_gf topic]
        B[PostgreSQL<br/>env.env_points_flink]
    end
    
    subgraph "Flink集群"
        C[JobManager<br/>2核4GB]
        D[TaskManager-1<br/>4核8GB]
        E[TaskManager-2<br/>4核8GB]
    end
    
    subgraph "目标存储"
        F[PostgreSQL<br/>env_2051_gf]
        G[PostgreSQL<br/>env_2061_gf]
        H[PostgreSQL<br/>env_2031_gf]
    end
    
    subgraph "监控系统"
        I[Prometheus]
        J[Grafana]
        K[AlertManager]
    end
    
    A --> D
    A --> E
    B --> D
    B --> E
    D --> F
    D --> G
    D --> H
    E --> F
    E --> G
    E --> H
    
    C --> I
    D --> I
    E --> I
    I --> J
    I --> K
```

## 运维管理

### 日常运维
- **健康检查**: 每日检查集群和作业状态
- **分区维护**: 定时创建未来分区，清理历史分区
- **数据质量**: 定期检查数据完整性和质量
- **性能监控**: 持续监控系统性能指标

### 故障处理
- **作业重启**: 自动重启策略 + 手动干预
- **数据恢复**: 基于Savepoint的快速恢复
- **分区修复**: 手动创建缺失分区
- **性能调优**: 根据监控数据调整资源配置

## 扩展性设计

### 水平扩展
- 增加TaskManager实例
- 提高作业并行度
- 扩展Kafka分区数

### 垂直扩展
- 增加单个TaskManager资源
- 优化JVM参数
- 调整批次大小和间隔

### 存储扩展
- 增加PostgreSQL存储容量
- 优化分区策略
- 调整数据保留策略

## 注意事项

### 开发注意事项
1. **动态字段处理**: factors字段中的因子编码是动态的，不能硬编码
2. **分区命名**: 严格按照命名规范创建分区表
3. **JSONB优化**: 合理使用GIN索引优化JSONB查询
4. **错误处理**: 完善的异常处理，避免单条错误数据影响整体处理

### 运维注意事项
1. **资源监控**: 密切关注内存使用，防止OOM
2. **分区管理**: 定期检查分区创建情况，及时处理异常
3. **数据备份**: 重要配置和状态数据要定期备份
4. **版本升级**: 制定详细的升级计划，确保平滑升级

## 联系信息

如有技术问题或需要支持，请联系项目团队：
- 架构设计: [架构师]
- 开发实现: [开发团队]
- 运维支持: [运维团队]

---

**文档版本**: v1.0  
**最后更新**: 2025-01-26  
**适用版本**: Flink 1.17.2, Kafka 2.x, PostgreSQL 12+
