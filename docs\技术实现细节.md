# 技术实现细节

## 1. 分区管理详细设计

### 1.1 分区管理器架构

```java
// 分区管理器接口
public interface PartitionManager {
    void ensurePartitionExists(String dataType, String pointType, Timestamp dataTime);
    boolean partitionExists(String partitionName);
    void createPartition(PartitionRule rule, String pointType, Timestamp dataTime);
}

// 分区规则配置
public class PartitionRule {
    private final String tableName;
    private final PartitionType partitionType;
    
    public enum PartitionType {
        MONTHLY,  // 按月分区
        YEARLY,   // 按年分区  
        NONE      // 不分区
    }
}
```

### 1.2 分区SQL生成策略

#### 1级分区表创建模板
```sql
-- point_type 分区
CREATE TABLE IF NOT EXISTS env.env_{data_type}_gf_{point_type} 
PARTITION OF env.env_{data_type}_gf 
FOR VALUES IN ('{point_type}')
{partition_by_clause};
```

#### 2级分区表创建模板
```sql
-- 时间范围分区
CREATE TABLE IF NOT EXISTS env.env_{data_type}_gf_{point_type}_{time_suffix} 
PARTITION OF env.env_{data_type}_gf_{point_type} 
({constraints}) 
FOR VALUES FROM ('{start_time}') TO ('{end_time}');

-- 索引创建
CREATE INDEX IF NOT EXISTS idx_env_{data_type}_gf_{point_type}_{time_suffix}_dt 
ON env.env_{data_type}_gf_{point_type}_{time_suffix} 
USING btree (data_time);
```

### 1.3 分区命名规则

| 数据类型 | 分区级别 | 命名规则 | 示例 |
|---------|---------|----------|------|
| 2051 | 1级 | env_2051_gf_{point_type} | env_2051_gf_31 |
| 2051 | 2级 | env_2051_gf_{point_type}_{yyyy_MM} | env_2051_gf_31_2025_01 |
| 2061 | 1级 | env_2061_gf_{point_type} | env_2061_gf_31 |
| 2061 | 2级 | env_2061_gf_{point_type}_{yyyy} | env_2061_gf_31_2025 |
| 2031 | 1级 | env_2031_gf_{point_type} | env_2031_gf_31 |

### 1.4 分区缓存设计

```java
// 分区缓存配置（小数据量场景优化）
public class PartitionCache {
    // 本地缓存配置 - 针对小数据量场景优化
    private final Cache<String, Boolean> partitionExistsCache = Caffeine.newBuilder()
        .maximumSize(100)             // 最大缓存100个分区（可配置）
        .expireAfterWrite(30, TimeUnit.MINUTES)  // 30分钟过期（可配置）
        .recordStats()                // 启用统计
        .build();

    // 缓存键格式: {data_type}_{point_type}_{time_suffix}
    public String buildCacheKey(String dataType, String pointType, String timeSuffix) {
        return String.format("%s_%s_%s", dataType, pointType, timeSuffix);
    }

    // 支持配置化的缓存参数
    public static PartitionCache create(int maxSize, int expireMinutes) {
        return new PartitionCache(maxSize, expireMinutes);
    }
}
```

## 2. 动态路由实现

### 2.1 数据流分流器

```java
// 数据类型路由器
public class DataTypeRouter extends ProcessFunction<RowData, RowData> {
    
    // 定义侧输出流标签
    private static final OutputTag<RowData> MINUTE_DATA_TAG = 
        new OutputTag<RowData>("minute-data", TypeInformation.of(RowData.class));
    private static final OutputTag<RowData> HOUR_DATA_TAG = 
        new OutputTag<RowData>("hour-data", TypeInformation.of(RowData.class));
    private static final OutputTag<RowData> DAY_DATA_TAG = 
        new OutputTag<RowData>("day-data", TypeInformation.of(RowData.class));
    
    @Override
    public void processElement(RowData value, Context ctx, Collector<RowData> out) {
        String dataType = value.getString(2).toString(); // data_type字段位置
        
        switch (dataType) {
            case "2051":
                ctx.output(MINUTE_DATA_TAG, value);
                break;
            case "2061":
                ctx.output(HOUR_DATA_TAG, value);
                break;
            case "2031":
                ctx.output(DAY_DATA_TAG, value);
                break;
            default:
                // 未知类型数据输出到主流进行错误处理
                out.collect(value);
        }
    }
}
```

### 2.2 动态Sink工厂

```java
// JDBC Sink工厂
public class DynamicJdbcSinkFactory {
    
    // 表配置映射
    private static final Map<String, TableConfig> TABLE_CONFIGS = Map.of(
        "2051", new TableConfig("env_2051_gf", "分钟数据表"),
        "2061", new TableConfig("env_2061_gf", "小时数据表"),
        "2031", new TableConfig("env_2031_gf", "日数据表")
    );
    
    public static JdbcSink<RowData> createSink(String dataType) {
        TableConfig config = TABLE_CONFIGS.get(dataType);
        
        return JdbcSink.<RowData>sink(
            generateInsertSQL(config.getTableName()),
            createJdbcStatementBuilder(),
            JdbcExecutionOptions.builder()
                .withBatchSize(100)               // 批次大小（小数据量优化）
                .withBatchIntervalMs(10000)       // 批次间隔（小数据量优化）
                .withMaxRetries(3)                // 最大重试次数
                .build(),
            createJdbcConnectionOptions(config)
        );
    }
    
    // 生成插入SQL
    private static String generateInsertSQL(String tableName) {
        return String.format(
            "INSERT INTO env.%s " +
            "(point_id, data_time, data_type, qn, st, mncode, flag, cp_ext, " +
            "gene_json, main_ext, auto_audit, ext_property, point_type, write_time, " +
            "source, operation_customer_id, gene_json_std, water_gene_label, " +
            "water_label, comm_rule, label_json, gene_trans_json) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, " +
            "?::jsonb, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb, ?::jsonb) " +
            "ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source) " +
            "DO UPDATE SET " +
            "gene_json = EXCLUDED.gene_json, " +
            "main_ext = EXCLUDED.main_ext, " +
            "gene_trans_json = EXCLUDED.gene_trans_json, " +
            "write_time = EXCLUDED.write_time",
            tableName
        );
    }
}
```

## 3. 自定义函数实现

### 3.1 动态因子映射函数

```java
@FunctionHint(output = @DataTypeHint("STRING"))
public class DynamicGeneTransMappingFunction extends ScalarFunction {
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    public String eval(String geneCodeMapping, String factorsJson) {
        try {
            if (geneCodeMapping == null || factorsJson == null) {
                return "{}";
            }
            
            // 解析 gene_code_mapping
            Map<String, String> codeMapping = objectMapper.readValue(geneCodeMapping, 
                new TypeReference<Map<String, String>>() {});
            
            // 解析 factors JSON 获取所有动态的 factor_code
            JsonNode factorsNode = objectMapper.readTree(factorsJson);
            Map<String, String> reverseMapping = new HashMap<>();
            
            // 遍历 factors 中的所有 key
            Iterator<String> fieldNames = factorsNode.fieldNames();
            while (fieldNames.hasNext()) {
                String factorCode = fieldNames.next();
                String mappedValue = codeMapping.get(factorCode);
                if (mappedValue != null) {
                    // 构建反向映射
                    reverseMapping.put(mappedValue, factorCode);
                }
            }
            
            return objectMapper.writeValueAsString(reverseMapping);
            
        } catch (Exception e) {
            // 记录错误日志
            LOG.warn("Failed to build gene_trans_json mapping", e);
            return "{}";
        }
    }
}
```

### 3.2 分区检查函数

```java
public class PartitionEnsureMapFunction extends RichMapFunction<RowData, RowData> {
    
    private final String dataType;
    private transient PartitionManager partitionManager;
    private transient PartitionCache partitionCache;
    
    public PartitionEnsureMapFunction(String dataType) {
        this.dataType = dataType;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.partitionManager = new PartitionManagerImpl();
        this.partitionCache = new PartitionCache();
    }
    
    @Override
    public RowData map(RowData value) throws Exception {
        // 提取分区相关字段
        String pointType = value.getString(12).toString();
        Timestamp dataTime = value.getTimestamp(1, 3);
        
        // 构建分区缓存键
        String timeSuffix = calculateTimeSuffix(dataType, dataTime);
        String cacheKey = partitionCache.buildCacheKey(dataType, pointType, timeSuffix);
        
        // 检查缓存，如果不存在则确保分区存在
        if (!partitionCache.isPartitionExists(cacheKey)) {
            try {
                partitionManager.ensurePartitionExists(dataType, pointType, dataTime);
                partitionCache.markPartitionExists(cacheKey);
            } catch (Exception e) {
                LOG.warn("Failed to ensure partition exists for key: {}", cacheKey, e);
                // 不阻断数据流，继续处理
            }
        }
        
        return value;
    }
    
    private String calculateTimeSuffix(String dataType, Timestamp dataTime) {
        LocalDateTime localDateTime = dataTime.toLocalDateTime();
        switch (dataType) {
            case "2051":
                return localDateTime.format(DateTimeFormatter.ofPattern("yyyy_MM"));
            case "2061":
                return localDateTime.format(DateTimeFormatter.ofPattern("yyyy"));
            default:
                return "";
        }
    }
}
```

## 4. 配置管理

### 4.1 Flink作业配置（小数据量场景优化）

```yaml
# flink-conf.yaml - 小数据量场景配置
execution:
  checkpointing:
    mode: EXACTLY_ONCE
    interval: 300s                    # 5分钟间隔（小数据量可适当延长）
    timeout: 10min
    max-concurrent-checkpoints: 1
    min-pause-between-checkpoints: 30s

restart-strategy:
  type: exponential-delay
  max-failures-per-interval: 5       # 减少重试次数
  failure-rate-interval: 10min
  delay: 2s
  max-delay: 1min                     # 减少最大延迟
  jitter-factor: 0.1

# 并行度配置（可参数化）
parallelism:
  default: 2                          # 默认并行度（可配置）
  source: 1                           # 源并行度（可配置）
  process: 2                          # 处理并行度（可配置）
  sink: 1                             # 写入并行度（可配置）

taskmanager:
  memory:
    process-size: 4gb                 # 小数据量场景减少内存
    managed-fraction: 0.4             # 减少状态存储内存比例
  network:
    memory-fraction: 0.1

state:
  backend: rocksdb
  backend.incremental: true
  checkpoints.dir: file:///opt/flink/checkpoints  # 本地存储（简化部署）
```

### 4.2 连接器配置（小数据量场景优化）

```properties
# Kafka配置
kafka.bootstrap.servers=kafka-cluster:9092
kafka.group.id=flink-gf-consumer
kafka.auto.offset.reset=latest
kafka.enable.auto.commit=false
kafka.max.poll.records=100          # 小数据量减少批次大小

# PostgreSQL配置（小数据量优化）
postgresql.url=***************************************
postgresql.username=flink_user
postgresql.password=flink_password
postgresql.driver=org.postgresql.Driver
postgresql.connection.max-pool-size=2    # 减少连接池大小
postgresql.connection.timeout=30000

# 缓存配置（可参数化）
partition.cache.max-size=100             # 分区缓存大小
partition.cache.expire-minutes=30        # 分区缓存过期时间
lookup.cache.max-rows=1000               # Lookup缓存大小
lookup.cache.ttl=30min                   # Lookup缓存TTL

# 批量写入配置（可参数化）
sink.batch.size=100                      # 批次大小
sink.batch.interval.seconds=10           # 批次间隔
```

## 5. 监控指标设计

### 5.1 业务指标

```java
// 业务监控指标
public class BusinessMetrics {
    
    // 数据处理量指标
    private final Counter recordsProcessedCounter = Counter.build()
        .name("records_processed_total")
        .help("Total number of records processed")
        .labelNames("data_type", "point_type")
        .register();
    
    // 数据处理延迟指标
    private final Histogram processingLatencyHistogram = Histogram.build()
        .name("processing_latency_seconds")
        .help("Processing latency in seconds")
        .labelNames("data_type")
        .buckets(0.1, 0.5, 1.0, 5.0, 10.0, 30.0)
        .register();
    
    // 分区创建指标
    private final Counter partitionCreatedCounter = Counter.build()
        .name("partition_created_total")
        .help("Total number of partitions created")
        .labelNames("data_type", "point_type")
        .register();
    
    // 错误率指标
    private final Counter errorCounter = Counter.build()
        .name("processing_errors_total")
        .help("Total number of processing errors")
        .labelNames("error_type", "data_type")
        .register();
}
```

### 5.2 系统指标

```java
// 系统监控指标
public class SystemMetrics {
    
    // JVM内存使用
    private final Gauge jvmMemoryUsedGauge = Gauge.build()
        .name("jvm_memory_used_bytes")
        .help("JVM memory used in bytes")
        .labelNames("memory_type")
        .register();
    
    // 数据库连接池
    private final Gauge dbConnectionPoolGauge = Gauge.build()
        .name("db_connection_pool_active")
        .help("Active database connections")
        .labelNames("pool_name")
        .register();
    
    // Kafka消费延迟
    private final Gauge kafkaConsumerLagGauge = Gauge.build()
        .name("kafka_consumer_lag")
        .help("Kafka consumer lag")
        .labelNames("topic", "partition")
        .register();
}
```

## 6. 异常处理策略

### 6.1 异常分类

| 异常类型 | 处理策略 | 影响范围 |
|---------|---------|----------|
| JSON解析异常 | 记录错误日志，跳过该条记录 | 单条记录 |
| 数据库连接异常 | 重试机制，超时后重启作业 | 整个作业 |
| 分区创建异常 | 记录警告，不阻断数据流 | 单个分区 |
| 数据关联失败 | 记录到错误表，继续处理 | 单条记录 |

### 6.2 重试机制

```java
// 重试配置
public class RetryConfig {
    public static final RetryPolicy DEFAULT_RETRY_POLICY = RetryPolicy.builder()
        .maxAttempts(3)
        .delay(Duration.ofSeconds(2))
        .maxDelay(Duration.ofMinutes(2))
        .backoffMultiplier(2.0)
        .jitter(0.1)
        .build();
}
```

## 7. 性能调优参数

### 7.1 内存调优

```yaml
# TaskManager内存配置
taskmanager.memory.process-size: 8gb
taskmanager.memory.flink-size: 7gb
taskmanager.memory.managed-fraction: 0.6
taskmanager.memory.network-fraction: 0.1

# JVM参数
env.java.opts: >
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+UnlockExperimentalVMOptions
  -XX:+UseCGroupMemoryLimitForHeap
```

### 7.2 资源调优建议（参数化配置）

**根据数据量调整配置**:
```yaml
# 极小数据量 (< 1万条/小时) - 推荐配置
parallelism.default: 1
parallelism.source: 1
parallelism.process: 1
parallelism.sink: 1
taskmanager.memory.process.size: 2gb
taskmanager.numberOfTaskSlots: 1

# 小数据量 (1-10万条/小时)
parallelism.default: 2
parallelism.source: 1
parallelism.process: 2
parallelism.sink: 1
taskmanager.memory.process.size: 4gb
taskmanager.numberOfTaskSlots: 2

# 中等数据量 (10-100万条/小时) - 需要时扩展
parallelism.default: 4
parallelism.source: 2
parallelism.process: 4
parallelism.sink: 2
taskmanager.memory.process.size: 8gb
taskmanager.numberOfTaskSlots: 4
```

### 7.3 网络调优（小数据量场景优化）

```yaml
# 网络缓冲区配置 - 小数据量优化
taskmanager.network.memory.fraction: 0.05    # 减少网络内存占用
taskmanager.network.memory.min: 32mb         # 减少最小网络内存
taskmanager.network.memory.max: 256mb        # 减少最大网络内存
taskmanager.network.numberOfBuffers: 512     # 减少缓冲区数量
```

### 7.4 状态后端调优（小数据量场景优化）

```yaml
# RocksDB配置 - 小数据量优化
state.backend.rocksdb.block.cache-size: 64mb     # 减少块缓存
state.backend.rocksdb.writebuffer.size: 16mb     # 减少写缓冲区
state.backend.rocksdb.writebuffer.count: 2       # 减少写缓冲区数量
state.backend.rocksdb.writebuffer.number-to-merge: 1  # 减少合并数量
```

## 8. 部署清单

### 8.1 依赖组件版本

| 组件 | 版本 | 说明 |
|------|------|------|
| Flink | 1.17.2 | 流处理引擎 |
| Kafka | 2.8.0+ | 消息队列 |
| PostgreSQL | 12+ | 数据库 |
| Java | 8+ | 运行环境 |

### 8.2 必需的Flink连接器

```xml
<!-- pom.xml依赖 -->
<dependencies>
    <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-connector-kafka</artifactId>
        <version>1.17.2</version>
    </dependency>
    <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-connector-jdbc</artifactId>
        <version>1.17.2</version>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.5.0</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.14.2</version>
    </dependency>
</dependencies>
```

这份技术实现细节文档提供了具体的代码架构和配置参数，为实际开发提供了详细的技术指导。
