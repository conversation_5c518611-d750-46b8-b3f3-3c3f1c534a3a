package com.example.gf.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 国发数据处理配置类
 */
public class GfConfig {
    
    private final Properties properties;
    
    public GfConfig() {
        this.properties = new Properties();
        loadProperties();
    }
    
    public GfConfig(String configFile) {
        this.properties = new Properties();
        loadProperties(configFile);
    }
    
    private void loadProperties() {
        loadProperties("application.properties");
    }
    
    private void loadProperties(String configFile) {
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(configFile)) {
            if (input == null) {
                throw new RuntimeException("Unable to find " + configFile);
            }
            properties.load(input);
        } catch (IOException e) {
            throw new RuntimeException("Failed to load configuration", e);
        }
    }
    
    // Kafka配置
    public String getKafkaBootstrapServers() {
        return properties.getProperty("kafka.bootstrap.servers", "localhost:9092");
    }
    
    public String getKafkaTopic() {
        return properties.getProperty("kafka.topic", "t212_gf");
    }
    
    public String getKafkaGroupId() {
        return properties.getProperty("kafka.group.id", "flink-gf-consumer");
    }
    
    public String getKafkaAutoOffsetReset() {
        return properties.getProperty("kafka.auto.offset.reset", "latest");
    }
    
    // PostgreSQL配置
    public String getPostgresqlUrl() {
        return properties.getProperty("postgresql.url", "*****************************************");
    }
    
    public String getPostgresqlUsername() {
        return properties.getProperty("postgresql.username", "flink_user");
    }
    
    public String getPostgresqlPassword() {
        return properties.getProperty("postgresql.password", "flink_password");
    }
    
    public String getPostgresqlDriver() {
        return properties.getProperty("postgresql.driver", "org.postgresql.Driver");
    }
    
    public int getPostgresqlMaxPoolSize() {
        return Integer.parseInt(properties.getProperty("postgresql.connection.max-pool-size", "2"));
    }
    
    public int getPostgresqlConnectionTimeout() {
        return Integer.parseInt(properties.getProperty("postgresql.connection.timeout", "30000"));
    }
    
    // 并行度配置
    public int getDefaultParallelism() {
        return Integer.parseInt(properties.getProperty("parallelism.default", "2"));
    }
    
    public int getSourceParallelism() {
        return Integer.parseInt(properties.getProperty("parallelism.source", "1"));
    }
    
    public int getProcessParallelism() {
        return Integer.parseInt(properties.getProperty("parallelism.process", "2"));
    }
    
    public int getSinkParallelism() {
        return Integer.parseInt(properties.getProperty("parallelism.sink", "1"));
    }
    
    // 批量写入配置
    public int getSinkBatchSize() {
        return Integer.parseInt(properties.getProperty("sink.batch.size", "100"));
    }
    
    public int getSinkBatchIntervalSeconds() {
        return Integer.parseInt(properties.getProperty("sink.batch.interval.seconds", "10"));
    }
    
    // 缓存配置
    public int getPartitionCacheMaxSize() {
        return Integer.parseInt(properties.getProperty("partition.cache.max-size", "100"));
    }
    
    public int getPartitionCacheExpireMinutes() {
        return Integer.parseInt(properties.getProperty("partition.cache.expire-minutes", "30"));
    }
    
    public int getLookupCacheMaxRows() {
        return Integer.parseInt(properties.getProperty("lookup.cache.max-rows", "1000"));
    }
    
    public int getLookupCacheTtlMinutes() {
        return Integer.parseInt(properties.getProperty("lookup.cache.ttl-minutes", "30"));
    }
    
    // Checkpoint配置
    public int getCheckpointIntervalSeconds() {
        return Integer.parseInt(properties.getProperty("checkpoint.interval.seconds", "300"));
    }
    
    public int getCheckpointTimeoutMinutes() {
        return Integer.parseInt(properties.getProperty("checkpoint.timeout.minutes", "10"));
    }
    
    // 日志配置
    public String getLoggingLevel() {
        return properties.getProperty("logging.level", "INFO");
    }
    
    // 获取原始Properties对象
    public Properties getProperties() {
        return properties;
    }
    
    // 获取指定属性值
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
}
